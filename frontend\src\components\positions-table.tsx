"use client"

import * as React from "react"
import Image from "next/image"
import { Share2, <PERSON><PERSON><PERSON>, DollarSign, ChevronUp, ChevronDown, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DashboardMatch } from "@/lib/betexplorer-types"

interface PositionsTableProps {
  className?: string
  // Props antigas (para compatibilidade)
  gameTitle?: string
  league?: string
  time?: string
  // Props novas (dados reais)
  matchData?: DashboardMatch
  loading?: boolean
  error?: string | null
  onRefresh?: () => void
}

export default function PositionsTable({
  className,
  // Props antigas (fallback)
  gameTitle = "Real Madrid x Inter de Milão",
  league = "Champions League",
  time = "Hoje, 20:00",
  // Props novas
  matchData,
  loading = false,
  error = null,
  onRefresh
}: PositionsTableProps) {
  const [activeMarket, setActiveMarket] = React.useState("1X2")
  const [isCollapsed, setIsCollapsed] = React.useState(false)

  // Determinar dados a serem exibidos (priorizar matchData)
  const displayData = React.useMemo(() => {
    if (matchData) {
      return {
        homeTeam: matchData.homeTeam,
        awayTeam: matchData.awayTeam,
        homeTeamLogo: matchData.homeTeamLogo,
        awayTeamLogo: matchData.awayTeamLogo,
        score: matchData.score,
        minute: matchData.minute,
        finished: matchData.finished,
        competition: matchData.competition,
        country: matchData.country,
        isLive: matchData.isLive,
        odds: matchData.odds
      }
    }

    // Fallback para dados estáticos
    const teams = gameTitle.split(' x ')
    return {
      homeTeam: teams[0]?.trim() || 'Time Casa',
      awayTeam: teams[1]?.trim() || 'Time Visitante',
      homeTeamLogo: undefined,
      awayTeamLogo: undefined,
      score: '0:0',
      minute: 0,
      finished: false,
      competition: league,
      country: '',
      isLive: false,
      odds: {}
    }
  }, [matchData, gameTitle, league])

  // Função para obter o escudo do time
  const getTeamShield = (teamName: string) => {
    const lowerName = teamName.toLowerCase()
    console.log('Checking team:', teamName, 'lowercase:', lowerName) // Debug
    if (lowerName.includes('real madrid')) {
      console.log('Found Real Madrid shield') // Debug
      return '/real madrid.png'
    }
    if (lowerName.includes('inter')) {
      console.log('Found Inter shield') // Debug
      return '/inter shield.png'
    }
    // Para outros times, retornamos um placeholder ou null
    // Você pode adicionar mais escudos conforme necessário
    console.log('No shield found for:', teamName) // Debug
    return null
  }

  // Função para renderizar o título com escudos (usando dados reais)
  const renderGameTitleWithShields = () => {
    const homeTeam = displayData.homeTeam
    const awayTeam = displayData.awayTeam
    const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam)
    const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam)

    return (
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          {homeShield && (
            <Image
              src={homeShield}
              alt={homeTeam}
              width={20}
              height={20}
              className="rounded-sm"
            />
          )}
          <span className="text-lg font-semibold text-foreground">
            {homeTeam}
          </span>
        </div>

        {/* Placar e status do jogo */}
        <div className="flex flex-col items-center">
          <span className="text-lg font-semibold text-muted-foreground">
            {displayData.score !== '0:0' ? displayData.score : 'x'}
          </span>
          {displayData.isLive && (
            <span className="text-xs text-green-500 font-medium">
              {displayData.minute}'
            </span>
          )}
          {displayData.finished && (
            <span className="text-xs text-muted-foreground">
              FT
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold text-foreground">
            {awayTeam}
          </span>
          {awayShield && (
            <Image
              src={awayShield}
              alt={awayTeam}
              width={20}
              height={20}
              className="rounded-sm"
            />
          )}
        </div>
      </div>
    )
  }

  const markets = [
    { id: "1X2", label: "1X2 Stats" },
    { id: "OU", label: "O/U" },
    { id: "AH", label: "AH" },
    { id: "DNB", label: "DNB" },
    { id: "DC", label: "DC" },
    { id: "BTTS", label: "BTTS" },
  ]

  return (
    <div className={`bg-background flex flex-col h-full ${className}`}>
      {/* Container da tabela - sempre visível mas com conteúdo condicional */}
      <div className={`${isCollapsed ? 'flex-shrink-0' : 'flex-1'} m-2 mx-4 border rounded-lg overflow-auto bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent`}>

        {/* Nome do Jogo - sempre visível */}
        <div className="px-6 py-6  border-border">
          <div className="flex items-center justify-between">
            <div>
              {renderGameTitleWithShields()}
              <p className="text-sm text-muted-foreground mt-1">
                {displayData.competition}
                {displayData.country && ` • ${displayData.country}`}
                {loading && ' • Carregando...'}
                {error && ' • Erro ao carregar'}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {/* Botão de refresh (só aparece se onRefresh estiver disponível) */}
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-8 w-8 p-0 text-muted-foreground hover:text-foreground ${loading ? 'animate-spin' : ''}`}
                  onClick={onRefresh}
                  disabled={loading}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}

              {/* Indicador de status ao vivo */}
              {displayData.isLive && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-500 font-medium">AO VIVO</span>
                </div>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de compartilhar */}}
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de configurações */}}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de dólar */}}
              >
                <DollarSign className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Conteúdo da tabela - só visível quando expandido */}
        {!isCollapsed && (
          <>
            {/* Toggles de Mercados */}
            <div className="px-6 py-4 border-b border-border">
              <div className="flex items-center gap-2">
                {markets.map((market) => (
                  <button
                    key={market.id}
                    onClick={() => setActiveMarket(market.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeMarket === market.id
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground"
                    }`}
                  >
                    {market.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tabela */}
            <table className="w-full text-sm">
              <thead className=" backdrop-blur-sm">
                <tr>
                  <th className="text-left p-3 font-medium text-muted-foreground">Type</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Size (ETH)</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Entry Price</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Margin Required</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Margin Weight</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Mark Price</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">PnL</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Status</th>
                </tr>
              </thead>
              <tbody>
                {/* Linhas vazias para demonstração */}
                {Array.from({ length: 8 }).map((_, index) => (
                  <tr key={index} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </>
        )}
      </div>

    
    </div>
  )
}
