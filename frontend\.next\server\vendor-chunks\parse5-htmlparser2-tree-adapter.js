"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse5-htmlparser2-tree-adapter";
exports.ids = ["vendor-chunks/parse5-htmlparser2-tree-adapter"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adapter: () => (/* binding */ adapter),\n/* harmony export */   serializeDoctypeContent: () => (/* binding */ serializeDoctypeContent)\n/* harmony export */ });\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\nfunction enquoteDoctypeId(id) {\n    const quote = id.includes('\"') ? \"'\" : '\"';\n    return quote + id + quote;\n}\n/** @internal */\nfunction serializeDoctypeContent(name, publicId, systemId) {\n    let str = '!DOCTYPE ';\n    if (name) {\n        str += name;\n    }\n    if (publicId) {\n        str += ` PUBLIC ${enquoteDoctypeId(publicId)}`;\n    }\n    else if (systemId) {\n        str += ' SYSTEM';\n    }\n    if (systemId) {\n        str += ` ${enquoteDoctypeId(systemId)}`;\n    }\n    return str;\n}\nconst adapter = {\n    // Re-exports from domhandler\n    isCommentNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isComment,\n    isElementNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag,\n    isTextNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isText,\n    //Node construction\n    createDocument() {\n        const node = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n        node['x-mode'] = parse5__WEBPACK_IMPORTED_MODULE_0__.html.DOCUMENT_MODE.NO_QUIRKS;\n        return node;\n    },\n    createDocumentFragment() {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n    },\n    createElement(tagName, namespaceURI, attrs) {\n        const attribs = Object.create(null);\n        const attribsNamespace = Object.create(null);\n        const attribsPrefix = Object.create(null);\n        for (let i = 0; i < attrs.length; i++) {\n            const attrName = attrs[i].name;\n            attribs[attrName] = attrs[i].value;\n            attribsNamespace[attrName] = attrs[i].namespace;\n            attribsPrefix[attrName] = attrs[i].prefix;\n        }\n        const node = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Element(tagName, attribs, []);\n        node.namespace = namespaceURI;\n        node['x-attribsNamespace'] = attribsNamespace;\n        node['x-attribsPrefix'] = attribsPrefix;\n        return node;\n    },\n    createCommentNode(data) {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n    },\n    createTextNode(value) {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Text(value);\n    },\n    //Tree mutation\n    appendChild(parentNode, newNode) {\n        const prev = parentNode.children[parentNode.children.length - 1];\n        if (prev) {\n            prev.next = newNode;\n            newNode.prev = prev;\n        }\n        parentNode.children.push(newNode);\n        newNode.parent = parentNode;\n    },\n    insertBefore(parentNode, newNode, referenceNode) {\n        const insertionIdx = parentNode.children.indexOf(referenceNode);\n        const { prev } = referenceNode;\n        if (prev) {\n            prev.next = newNode;\n            newNode.prev = prev;\n        }\n        referenceNode.prev = newNode;\n        newNode.next = referenceNode;\n        parentNode.children.splice(insertionIdx, 0, newNode);\n        newNode.parent = parentNode;\n    },\n    setTemplateContent(templateElement, contentElement) {\n        adapter.appendChild(templateElement, contentElement);\n    },\n    getTemplateContent(templateElement) {\n        return templateElement.children[0];\n    },\n    setDocumentType(document, name, publicId, systemId) {\n        const data = serializeDoctypeContent(name, publicId, systemId);\n        let doctypeNode = document.children.find((node) => (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDirective)(node) && node.name === '!doctype');\n        if (doctypeNode) {\n            doctypeNode.data = data !== null && data !== void 0 ? data : null;\n        }\n        else {\n            doctypeNode = new domhandler__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction('!doctype', data);\n            adapter.appendChild(document, doctypeNode);\n        }\n        doctypeNode['x-name'] = name;\n        doctypeNode['x-publicId'] = publicId;\n        doctypeNode['x-systemId'] = systemId;\n    },\n    setDocumentMode(document, mode) {\n        document['x-mode'] = mode;\n    },\n    getDocumentMode(document) {\n        return document['x-mode'];\n    },\n    detachNode(node) {\n        if (node.parent) {\n            const idx = node.parent.children.indexOf(node);\n            const { prev, next } = node;\n            node.prev = null;\n            node.next = null;\n            if (prev) {\n                prev.next = next;\n            }\n            if (next) {\n                next.prev = prev;\n            }\n            node.parent.children.splice(idx, 1);\n            node.parent = null;\n        }\n    },\n    insertText(parentNode, text) {\n        const lastChild = parentNode.children[parentNode.children.length - 1];\n        if (lastChild && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isText)(lastChild)) {\n            lastChild.data += text;\n        }\n        else {\n            adapter.appendChild(parentNode, adapter.createTextNode(text));\n        }\n    },\n    insertTextBefore(parentNode, text, referenceNode) {\n        const prevNode = parentNode.children[parentNode.children.indexOf(referenceNode) - 1];\n        if (prevNode && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isText)(prevNode)) {\n            prevNode.data += text;\n        }\n        else {\n            adapter.insertBefore(parentNode, adapter.createTextNode(text), referenceNode);\n        }\n    },\n    adoptAttributes(recipient, attrs) {\n        for (let i = 0; i < attrs.length; i++) {\n            const attrName = attrs[i].name;\n            if (recipient.attribs[attrName] === undefined) {\n                recipient.attribs[attrName] = attrs[i].value;\n                recipient['x-attribsNamespace'][attrName] = attrs[i].namespace;\n                recipient['x-attribsPrefix'][attrName] = attrs[i].prefix;\n            }\n        }\n    },\n    //Tree traversing\n    getFirstChild(node) {\n        return node.children[0];\n    },\n    getChildNodes(node) {\n        return node.children;\n    },\n    getParentNode(node) {\n        return node.parent;\n    },\n    getAttrList(element) {\n        return element.attributes;\n    },\n    //Node data\n    getTagName(element) {\n        return element.name;\n    },\n    getNamespaceURI(element) {\n        return element.namespace;\n    },\n    getTextNodeContent(textNode) {\n        return textNode.data;\n    },\n    getCommentNodeContent(commentNode) {\n        return commentNode.data;\n    },\n    getDocumentTypeNodeName(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-name']) !== null && _a !== void 0 ? _a : '';\n    },\n    getDocumentTypeNodePublicId(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-publicId']) !== null && _a !== void 0 ? _a : '';\n    },\n    getDocumentTypeNodeSystemId(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-systemId']) !== null && _a !== void 0 ? _a : '';\n    },\n    //Node types\n    isDocumentTypeNode(node) {\n        return (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDirective)(node) && node.name === '!doctype';\n    },\n    // Source code location\n    setNodeSourceCodeLocation(node, location) {\n        if (location) {\n            node.startIndex = location.startOffset;\n            node.endIndex = location.endOffset;\n        }\n        node.sourceCodeLocation = location;\n    },\n    getNodeSourceCodeLocation(node) {\n        return node.sourceCodeLocation;\n    },\n    updateNodeSourceCodeLocation(node, endLocation) {\n        if (endLocation.endOffset != null)\n            node.endIndex = endLocation.endOffset;\n        node.sourceCodeLocation = {\n            ...node.sourceCodeLocation,\n            ...endLocation,\n        };\n    },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\n");

/***/ })

};
;