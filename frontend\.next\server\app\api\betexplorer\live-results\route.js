/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/betexplorer/live-results/route";
exports.ids = ["app/api/betexplorer/live-results/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-results%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-results%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-results%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-results%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-results%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-results%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_results_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/betexplorer/live-results/route.ts */ \"(rsc)/./src/app/api/betexplorer/live-results/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/betexplorer/live-results/route\",\n        pathname: \"/api/betexplorer/live-results\",\n        filename: \"route\",\n        bundlePath: \"app/api/betexplorer/live-results/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\api\\\\betexplorer\\\\live-results\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_results_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/betexplorer/live-results/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZiZXRleHBsb3JlciUyRmxpdmUtcmVzdWx0cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYmV0ZXhwbG9yZXIlMkZsaXZlLXJlc3VsdHMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZiZXRleHBsb3JlciUyRmxpdmUtcmVzdWx0cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBZG1pbmlzdHJhdG9yJTVDRG9jdW1lbnRzJTVDMyU1Q2Zyb250ZW5kJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNBZG1pbmlzdHJhdG9yJTVDRG9jdW1lbnRzJTVDMyU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEJmlzR2xvYmFsTm90Rm91bmRFbmFibGVkPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDZDtBQUNTO0FBQ087QUFDSztBQUNtQztBQUNqRDtBQUNPO0FBQ2Y7QUFDc0M7QUFDekI7QUFDTTtBQUNDO0FBQ2hCO0FBQytEO0FBQ2pJO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWEsT0FBb0MsSUFBSSxDQUFFO0FBQ3ZELGdCQUFnQixNQUF1QztBQUN2RDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjtBQUNuRjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLEtBQXFCLEVBQUUsRUFFMUIsQ0FBQztBQUNOO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixPQUF3QztBQUN2RTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxvSkFBb0o7QUFDaEssOEJBQThCLDZGQUFnQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsNkZBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNEVBQVM7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLDhCQUE4Qiw2RUFBYztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsNEVBQWU7QUFDM0MsNEJBQTRCLDZFQUFnQjtBQUM1QyxvQkFBb0IseUdBQWtCLGtDQUFrQyxpSEFBc0I7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxnRkFBYztBQUMvRSwrREFBK0QseUNBQXlDO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFFBQVEsRUFBRSxNQUFNO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esa0JBQWtCO0FBQ2xCLHVDQUF1QyxRQUFRLEVBQUUsUUFBUTtBQUN6RDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0Msb0JBQW9CO0FBQ25FO0FBQ0EseUJBQXlCLDZFQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0Msc0ZBQXlCO0FBQ2pFO0FBQ0Esb0NBQW9DLDRFQUFzQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNKQUFzSixvRUFBYztBQUNwSywwSUFBMEksb0VBQWM7QUFDeEo7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLDZFQUFlO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSw4QkFBOEIsNkVBQVk7QUFDMUM7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QywyRkFBbUI7QUFDakU7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixrRUFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFJQUFxSSw2RUFBZTtBQUNwSjtBQUNBLDJHQUEyRyxpSEFBaUg7QUFDNU47QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsaUJBQWlCLDZFQUFjO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix3RkFBMkI7QUFDdkQsa0JBQWtCLDZFQUFjO0FBQ2hDLCtCQUErQiw0RUFBc0I7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsMEZBQXFCO0FBQ2xFO0FBQ0Esa0JBQWtCLDZFQUFZO0FBQzlCO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLDZFQUE2RSxnRkFBYztBQUMzRixpQ0FBaUMsUUFBUSxFQUFFLFFBQVE7QUFDbkQsMEJBQTBCLHVFQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsNENBQTRDLDZGQUFlO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDJGQUFtQjtBQUNyRDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2RUFBWTtBQUMxQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgeyBnZXRSZXF1ZXN0TWV0YSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JlcXVlc3QtbWV0YVwiO1xuaW1wb3J0IHsgZ2V0VHJhY2VyLCBTcGFuS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS90cmFjZXJcIjtcbmltcG9ydCB7IG5vcm1hbGl6ZUFwcFBhdGggfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FwcC1wYXRoc1wiO1xuaW1wb3J0IHsgTm9kZU5leHRSZXF1ZXN0LCBOb2RlTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYmFzZS1odHRwL25vZGVcIjtcbmltcG9ydCB7IE5leHRSZXF1ZXN0QWRhcHRlciwgc2lnbmFsRnJvbU5vZGVSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9uZXh0LXJlcXVlc3RcIjtcbmltcG9ydCB7IEJhc2VTZXJ2ZXJTcGFuIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3RyYWNlL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgZ2V0UmV2YWxpZGF0ZVJlYXNvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2luc3RydW1lbnRhdGlvbi91dGlsc1wiO1xuaW1wb3J0IHsgc2VuZFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvc2VuZC1yZXNwb25zZVwiO1xuaW1wb3J0IHsgZnJvbU5vZGVPdXRnb2luZ0h0dHBIZWFkZXJzLCB0b05vZGVPdXRnb2luZ0h0dHBIZWFkZXJzIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvd2ViL3V0aWxzXCI7XG5pbXBvcnQgeyBnZXRDYWNoZUNvbnRyb2xIZWFkZXIgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvY2FjaGUtY29udHJvbFwiO1xuaW1wb3J0IHsgSU5GSU5JVEVfQ0FDSEUsIE5FWFRfQ0FDSEVfVEFHU19IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9jb25zdGFudHNcIjtcbmltcG9ydCB7IE5vRmFsbGJhY2tFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9uby1mYWxsYmFjay1lcnJvci5leHRlcm5hbFwiO1xuaW1wb3J0IHsgQ2FjaGVkUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVzcG9uc2UtY2FjaGVcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERvY3VtZW50c1xcXFwzXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGJldGV4cGxvcmVyXFxcXGxpdmUtcmVzdWx0c1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYmV0ZXhwbG9yZXIvbGl2ZS1yZXN1bHRzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYmV0ZXhwbG9yZXIvbGl2ZS1yZXN1bHRzXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9iZXRleHBsb3Jlci9saXZlLXJlc3VsdHMvcm91dGVcIlxuICAgIH0sXG4gICAgZGlzdERpcjogcHJvY2Vzcy5lbnYuX19ORVhUX1JFTEFUSVZFX0RJU1RfRElSIHx8ICcnLFxuICAgIHByb2plY3REaXI6IHByb2Nlc3MuZW52Ll9fTkVYVF9SRUxBVElWRV9QUk9KRUNUX0RJUiB8fCAnJyxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRG9jdW1lbnRzXFxcXDNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYmV0ZXhwbG9yZXJcXFxcbGl2ZS1yZXN1bHRzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIocmVxLCByZXMsIGN0eCkge1xuICAgIHZhciBfbmV4dENvbmZpZ19leHBlcmltZW50YWw7XG4gICAgbGV0IHNyY1BhZ2UgPSBcIi9hcGkvYmV0ZXhwbG9yZXIvbGl2ZS1yZXN1bHRzL3JvdXRlXCI7XG4gICAgLy8gdHVyYm9wYWNrIGRvZXNuJ3Qgbm9ybWFsaXplIGAvaW5kZXhgIGluIHRoZSBwYWdlIG5hbWVcbiAgICAvLyBzbyB3ZSBuZWVkIHRvIHRvIHByb2Nlc3MgZHluYW1pYyByb3V0ZXMgcHJvcGVybHlcbiAgICAvLyBUT0RPOiBmaXggdHVyYm9wYWNrIHByb3ZpZGluZyBkaWZmZXJpbmcgdmFsdWUgZnJvbSB3ZWJwYWNrXG4gICAgaWYgKHByb2Nlc3MuZW52LlRVUkJPUEFDSykge1xuICAgICAgICBzcmNQYWdlID0gc3JjUGFnZS5yZXBsYWNlKC9cXC9pbmRleCQvLCAnJykgfHwgJy8nO1xuICAgIH0gZWxzZSBpZiAoc3JjUGFnZSA9PT0gJy9pbmRleCcpIHtcbiAgICAgICAgLy8gd2UgYWx3YXlzIG5vcm1hbGl6ZSAvaW5kZXggc3BlY2lmaWNhbGx5XG4gICAgICAgIHNyY1BhZ2UgPSAnLyc7XG4gICAgfVxuICAgIGNvbnN0IG11bHRpWm9uZURyYWZ0TW9kZSA9IHByb2Nlc3MuZW52Ll9fTkVYVF9NVUxUSV9aT05FX0RSQUZUX01PREU7XG4gICAgY29uc3QgcHJlcGFyZVJlc3VsdCA9IGF3YWl0IHJvdXRlTW9kdWxlLnByZXBhcmUocmVxLCByZXMsIHtcbiAgICAgICAgc3JjUGFnZSxcbiAgICAgICAgbXVsdGlab25lRHJhZnRNb2RlXG4gICAgfSk7XG4gICAgaWYgKCFwcmVwYXJlUmVzdWx0KSB7XG4gICAgICAgIHJlcy5zdGF0dXNDb2RlID0gNDAwO1xuICAgICAgICByZXMuZW5kKCdCYWQgUmVxdWVzdCcpO1xuICAgICAgICBjdHgud2FpdFVudGlsID09IG51bGwgPyB2b2lkIDAgOiBjdHgud2FpdFVudGlsLmNhbGwoY3R4LCBQcm9taXNlLnJlc29sdmUoKSk7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBjb25zdCB7IGJ1aWxkSWQsIHBhcmFtcywgbmV4dENvbmZpZywgaXNEcmFmdE1vZGUsIHByZXJlbmRlck1hbmlmZXN0LCByb3V0ZXJTZXJ2ZXJDb250ZXh0LCBpc09uRGVtYW5kUmV2YWxpZGF0ZSwgcmV2YWxpZGF0ZU9ubHlHZW5lcmF0ZWQsIHJlc29sdmVkUGF0aG5hbWUgfSA9IHByZXBhcmVSZXN1bHQ7XG4gICAgY29uc3Qgbm9ybWFsaXplZFNyY1BhZ2UgPSBub3JtYWxpemVBcHBQYXRoKHNyY1BhZ2UpO1xuICAgIGxldCBpc0lzciA9IEJvb2xlYW4ocHJlcmVuZGVyTWFuaWZlc3QuZHluYW1pY1JvdXRlc1tub3JtYWxpemVkU3JjUGFnZV0gfHwgcHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW3Jlc29sdmVkUGF0aG5hbWVdKTtcbiAgICBpZiAoaXNJc3IgJiYgIWlzRHJhZnRNb2RlKSB7XG4gICAgICAgIGNvbnN0IGlzUHJlcmVuZGVyZWQgPSBCb29sZWFuKHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tyZXNvbHZlZFBhdGhuYW1lXSk7XG4gICAgICAgIGNvbnN0IHByZXJlbmRlckluZm8gPSBwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXTtcbiAgICAgICAgaWYgKHByZXJlbmRlckluZm8pIHtcbiAgICAgICAgICAgIGlmIChwcmVyZW5kZXJJbmZvLmZhbGxiYWNrID09PSBmYWxzZSAmJiAhaXNQcmVyZW5kZXJlZCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBOb0ZhbGxiYWNrRXJyb3IoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBsZXQgY2FjaGVLZXkgPSBudWxsO1xuICAgIGlmIChpc0lzciAmJiAhcm91dGVNb2R1bGUuaXNEZXYgJiYgIWlzRHJhZnRNb2RlKSB7XG4gICAgICAgIGNhY2hlS2V5ID0gcmVzb2x2ZWRQYXRobmFtZTtcbiAgICAgICAgLy8gZW5zdXJlIC9pbmRleCBhbmQgLyBpcyBub3JtYWxpemVkIHRvIG9uZSBrZXlcbiAgICAgICAgY2FjaGVLZXkgPSBjYWNoZUtleSA9PT0gJy9pbmRleCcgPyAnLycgOiBjYWNoZUtleTtcbiAgICB9XG4gICAgY29uc3Qgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UgPSAvLyBJZiB3ZSdyZSBpbiBkZXZlbG9wbWVudCwgd2UgYWx3YXlzIHN1cHBvcnQgZHluYW1pYyBIVE1MXG4gICAgcm91dGVNb2R1bGUuaXNEZXYgPT09IHRydWUgfHwgLy8gSWYgdGhpcyBpcyBub3QgU1NHIG9yIGRvZXMgbm90IGhhdmUgc3RhdGljIHBhdGhzLCB0aGVuIGl0IHN1cHBvcnRzXG4gICAgLy8gZHluYW1pYyBIVE1MLlxuICAgICFpc0lzcjtcbiAgICAvLyBUaGlzIGlzIGEgcmV2YWxpZGF0aW9uIHJlcXVlc3QgaWYgdGhlIHJlcXVlc3QgaXMgZm9yIGEgc3RhdGljXG4gICAgLy8gcGFnZSBhbmQgaXQgaXMgbm90IGJlaW5nIHJlc3VtZWQgZnJvbSBhIHBvc3Rwb25lZCByZW5kZXIgYW5kXG4gICAgLy8gaXQgaXMgbm90IGEgZHluYW1pYyBSU0MgcmVxdWVzdCB0aGVuIGl0IGlzIGEgcmV2YWxpZGF0aW9uXG4gICAgLy8gcmVxdWVzdC5cbiAgICBjb25zdCBpc1JldmFsaWRhdGUgPSBpc0lzciAmJiAhc3VwcG9ydHNEeW5hbWljUmVzcG9uc2U7XG4gICAgY29uc3QgbWV0aG9kID0gcmVxLm1ldGhvZCB8fCAnR0VUJztcbiAgICBjb25zdCB0cmFjZXIgPSBnZXRUcmFjZXIoKTtcbiAgICBjb25zdCBhY3RpdmVTcGFuID0gdHJhY2VyLmdldEFjdGl2ZVNjb3BlU3BhbigpO1xuICAgIGNvbnN0IGNvbnRleHQgPSB7XG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgcHJlcmVuZGVyTWFuaWZlc3QsXG4gICAgICAgIHJlbmRlck9wdHM6IHtcbiAgICAgICAgICAgIGV4cGVyaW1lbnRhbDoge1xuICAgICAgICAgICAgICAgIGR5bmFtaWNJTzogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljSU8pLFxuICAgICAgICAgICAgICAgIGF1dGhJbnRlcnJ1cHRzOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmF1dGhJbnRlcnJ1cHRzKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlLFxuICAgICAgICAgICAgaW5jcmVtZW50YWxDYWNoZTogZ2V0UmVxdWVzdE1ldGEocmVxLCAnaW5jcmVtZW50YWxDYWNoZScpLFxuICAgICAgICAgICAgY2FjaGVMaWZlUHJvZmlsZXM6IChfbmV4dENvbmZpZ19leHBlcmltZW50YWwgPSBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbC5jYWNoZUxpZmUsXG4gICAgICAgICAgICBpc1JldmFsaWRhdGUsXG4gICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWwsXG4gICAgICAgICAgICBvbkNsb3NlOiAoY2IpPT57XG4gICAgICAgICAgICAgICAgcmVzLm9uKCdjbG9zZScsIGNiKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvbkFmdGVyVGFza0Vycm9yOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBvbkluc3RydW1lbnRhdGlvblJlcXVlc3RFcnJvcjogKGVycm9yLCBfcmVxdWVzdCwgZXJyb3JDb250ZXh0KT0+cm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnJvciwgZXJyb3JDb250ZXh0LCByb3V0ZXJTZXJ2ZXJDb250ZXh0KVxuICAgICAgICB9LFxuICAgICAgICBzaGFyZWRDb250ZXh0OiB7XG4gICAgICAgICAgICBidWlsZElkXG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IG5vZGVOZXh0UmVxID0gbmV3IE5vZGVOZXh0UmVxdWVzdChyZXEpO1xuICAgIGNvbnN0IG5vZGVOZXh0UmVzID0gbmV3IE5vZGVOZXh0UmVzcG9uc2UocmVzKTtcbiAgICBjb25zdCBuZXh0UmVxID0gTmV4dFJlcXVlc3RBZGFwdGVyLmZyb21Ob2RlTmV4dFJlcXVlc3Qobm9kZU5leHRSZXEsIHNpZ25hbEZyb21Ob2RlUmVzcG9uc2UocmVzKSk7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgaW52b2tlUm91dGVNb2R1bGUgPSBhc3luYyAoc3Bhbik9PntcbiAgICAgICAgICAgIHJldHVybiByb3V0ZU1vZHVsZS5oYW5kbGUobmV4dFJlcSwgY29udGV4dCkuZmluYWxseSgoKT0+e1xuICAgICAgICAgICAgICAgIGlmICghc3BhbikgcmV0dXJuO1xuICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlcyh7XG4gICAgICAgICAgICAgICAgICAgICdodHRwLnN0YXR1c19jb2RlJzogcmVzLnN0YXR1c0NvZGUsXG4gICAgICAgICAgICAgICAgICAgICduZXh0LnJzYyc6IGZhbHNlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3Qgcm9vdFNwYW5BdHRyaWJ1dGVzID0gdHJhY2VyLmdldFJvb3RTcGFuQXR0cmlidXRlcygpO1xuICAgICAgICAgICAgICAgIC8vIFdlIHdlcmUgdW5hYmxlIHRvIGdldCBhdHRyaWJ1dGVzLCBwcm9iYWJseSBPVEVMIGlzIG5vdCBlbmFibGVkXG4gICAgICAgICAgICAgICAgaWYgKCFyb290U3BhbkF0dHJpYnV0ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocm9vdFNwYW5BdHRyaWJ1dGVzLmdldCgnbmV4dC5zcGFuX3R5cGUnKSAhPT0gQmFzZVNlcnZlclNwYW4uaGFuZGxlUmVxdWVzdCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFVuZXhwZWN0ZWQgcm9vdCBzcGFuIHR5cGUgJyR7cm9vdFNwYW5BdHRyaWJ1dGVzLmdldCgnbmV4dC5zcGFuX3R5cGUnKX0nLiBQbGVhc2UgcmVwb3J0IHRoaXMgTmV4dC5qcyBpc3N1ZSBodHRwczovL2dpdGh1Yi5jb20vdmVyY2VsL25leHQuanNgKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZSA9IHJvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQucm91dGUnKTtcbiAgICAgICAgICAgICAgICBpZiAocm91dGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmFtZSA9IGAke21ldGhvZH0gJHtyb3V0ZX1gO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZXMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgJ25leHQucm91dGUnOiByb3V0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLnJvdXRlJzogcm91dGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAnbmV4dC5zcGFuX25hbWUnOiBuYW1lXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnVwZGF0ZU5hbWUobmFtZSk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKGAke21ldGhvZH0gJHtyZXEudXJsfWApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBoYW5kbGVSZXNwb25zZSA9IGFzeW5jIChjdXJyZW50U3Bhbik9PntcbiAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlR2VuZXJhdG9yID0gYXN5bmMgKHsgcHJldmlvdXNDYWNoZUVudHJ5IH0pPT57XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFnZXRSZXF1ZXN0TWV0YShyZXEsICdtaW5pbWFsTW9kZScpICYmIGlzT25EZW1hbmRSZXZhbGlkYXRlICYmIHJldmFsaWRhdGVPbmx5R2VuZXJhdGVkICYmICFwcmV2aW91c0NhY2hlRW50cnkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5zdGF0dXNDb2RlID0gNDA0O1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gb24tZGVtYW5kIHJldmFsaWRhdGUgYWx3YXlzIHNldHMgdGhpcyBoZWFkZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoJ3gtbmV4dGpzLWNhY2hlJywgJ1JFVkFMSURBVEVEJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMuZW5kKCdUaGlzIHBhZ2UgY291bGQgbm90IGJlIGZvdW5kJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGludm9rZVJvdXRlTW9kdWxlKGN1cnJlbnRTcGFuKTtcbiAgICAgICAgICAgICAgICAgICAgcmVxLmZldGNoTWV0cmljcyA9IGNvbnRleHQucmVuZGVyT3B0cy5mZXRjaE1ldHJpY3M7XG4gICAgICAgICAgICAgICAgICAgIGxldCBwZW5kaW5nV2FpdFVudGlsID0gY29udGV4dC5yZW5kZXJPcHRzLnBlbmRpbmdXYWl0VW50aWw7XG4gICAgICAgICAgICAgICAgICAgIC8vIEF0dGVtcHQgdXNpbmcgcHJvdmlkZWQgd2FpdFVudGlsIGlmIGF2YWlsYWJsZVxuICAgICAgICAgICAgICAgICAgICAvLyBpZiBpdCdzIG5vdCB3ZSBmYWxsYmFjayB0byBzZW5kUmVzcG9uc2UncyBoYW5kbGluZ1xuICAgICAgICAgICAgICAgICAgICBpZiAocGVuZGluZ1dhaXRVbnRpbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN0eC53YWl0VW50aWwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdHgud2FpdFVudGlsKHBlbmRpbmdXYWl0VW50aWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdXYWl0VW50aWwgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY2FjaGVUYWdzID0gY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZFRhZ3M7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSByZXF1ZXN0IGlzIGZvciBhIHN0YXRpYyByZXNwb25zZSwgd2UgY2FuIGNhY2hlIGl0IHNvIGxvbmdcbiAgICAgICAgICAgICAgICAgICAgLy8gYXMgaXQncyBub3QgZWRnZS5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzSXNyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBibG9iID0gYXdhaXQgcmVzcG9uc2UuYmxvYigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ29weSB0aGUgaGVhZGVycyBmcm9tIHRoZSByZXNwb25zZS5cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGhlYWRlcnMgPSB0b05vZGVPdXRnb2luZ0h0dHBIZWFkZXJzKHJlc3BvbnNlLmhlYWRlcnMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNhY2hlVGFncykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnNbTkVYVF9DQUNIRV9UQUdTX0hFQURFUl0gPSBjYWNoZVRhZ3M7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWhlYWRlcnNbJ2NvbnRlbnQtdHlwZSddICYmIGJsb2IudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnNbJ2NvbnRlbnQtdHlwZSddID0gYmxvYi50eXBlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmV2YWxpZGF0ZSA9IHR5cGVvZiBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkUmV2YWxpZGF0ZSA9PT0gJ3VuZGVmaW5lZCcgfHwgY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZFJldmFsaWRhdGUgPj0gSU5GSU5JVEVfQ0FDSEUgPyBmYWxzZSA6IGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRSZXZhbGlkYXRlO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhwaXJlID0gdHlwZW9mIGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRFeHBpcmUgPT09ICd1bmRlZmluZWQnIHx8IGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRFeHBpcmUgPj0gSU5GSU5JVEVfQ0FDSEUgPyB1bmRlZmluZWQgOiBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkRXhwaXJlO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ3JlYXRlIHRoZSBjYWNoZSBlbnRyeSBmb3IgdGhlIHJlc3BvbnNlLlxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY2FjaGVFbnRyeSA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBraW5kOiBDYWNoZWRSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9keTogQnVmZmVyLmZyb20oYXdhaXQgYmxvYi5hcnJheUJ1ZmZlcigpKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2FjaGVFbnRyeTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHNlbmQgcmVzcG9uc2Ugd2l0aG91dCBjYWNoaW5nIGlmIG5vdCBJU1JcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHNlbmRSZXNwb25zZShub2RlTmV4dFJlcSwgbm9kZU5leHRSZXMsIHJlc3BvbnNlLCBjb250ZXh0LnJlbmRlck9wdHMucGVuZGluZ1dhaXRVbnRpbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgICAgICAvLyBpZiB0aGlzIGlzIGEgYmFja2dyb3VuZCByZXZhbGlkYXRlIHdlIG5lZWQgdG8gcmVwb3J0XG4gICAgICAgICAgICAgICAgICAgIC8vIHRoZSByZXF1ZXN0IGVycm9yIGhlcmUgYXMgaXQgd29uJ3QgYmUgYnViYmxlZFxuICAgICAgICAgICAgICAgICAgICBpZiAocHJldmlvdXNDYWNoZUVudHJ5ID09IG51bGwgPyB2b2lkIDAgOiBwcmV2aW91c0NhY2hlRW50cnkuaXNTdGFsZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgcm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXJLaW5kOiAnQXBwIFJvdXRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVQYXRoOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JvdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlUmVhc29uOiBnZXRSZXZhbGlkYXRlUmVhc29uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICB9LCByb3V0ZXJTZXJ2ZXJDb250ZXh0KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnN0IGNhY2hlRW50cnkgPSBhd2FpdCByb3V0ZU1vZHVsZS5oYW5kbGVSZXNwb25zZSh7XG4gICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgIG5leHRDb25maWcsXG4gICAgICAgICAgICAgICAgY2FjaGVLZXksXG4gICAgICAgICAgICAgICAgcm91dGVLaW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICAgICAgICAgIGlzRmFsbGJhY2s6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICByZXZhbGlkYXRlT25seUdlbmVyYXRlZCxcbiAgICAgICAgICAgICAgICByZXNwb25zZUdlbmVyYXRvcixcbiAgICAgICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLy8gd2UgZG9uJ3QgY3JlYXRlIGEgY2FjaGVFbnRyeSBmb3IgSVNSXG4gICAgICAgICAgICBpZiAoIWlzSXNyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKGNhY2hlRW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IChfY2FjaGVFbnRyeV92YWx1ZSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZS5raW5kKSAhPT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9ST1VURSkge1xuICAgICAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZTE7XG4gICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YXJpYW50OiBhcHAtcm91dGUgcmVjZWl2ZWQgaW52YWxpZCBjYWNoZSBlbnRyeSAke2NhY2hlRW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IChfY2FjaGVFbnRyeV92YWx1ZTEgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUxLmtpbmR9YCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFNzAxXCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghZ2V0UmVxdWVzdE1ldGEocmVxLCAnbWluaW1hbE1vZGUnKSkge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoJ3gtbmV4dGpzLWNhY2hlJywgaXNPbkRlbWFuZFJldmFsaWRhdGUgPyAnUkVWQUxJREFURUQnIDogY2FjaGVFbnRyeS5pc01pc3MgPyAnTUlTUycgOiBjYWNoZUVudHJ5LmlzU3RhbGUgPyAnU1RBTEUnIDogJ0hJVCcpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRHJhZnQgbW9kZSBzaG91bGQgbmV2ZXIgYmUgY2FjaGVkXG4gICAgICAgICAgICBpZiAoaXNEcmFmdE1vZGUpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJywgJ3ByaXZhdGUsIG5vLWNhY2hlLCBuby1zdG9yZSwgbWF4LWFnZT0wLCBtdXN0LXJldmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGhlYWRlcnMgPSBmcm9tTm9kZU91dGdvaW5nSHR0cEhlYWRlcnMoY2FjaGVFbnRyeS52YWx1ZS5oZWFkZXJzKTtcbiAgICAgICAgICAgIGlmICghKGdldFJlcXVlc3RNZXRhKHJlcSwgJ21pbmltYWxNb2RlJykgJiYgaXNJc3IpKSB7XG4gICAgICAgICAgICAgICAgaGVhZGVycy5kZWxldGUoTkVYVF9DQUNIRV9UQUdTX0hFQURFUik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiBjYWNoZSBjb250cm9sIGlzIGFscmVhZHkgc2V0IG9uIHRoZSByZXNwb25zZSB3ZSBkb24ndFxuICAgICAgICAgICAgLy8gb3ZlcnJpZGUgaXQgdG8gYWxsb3cgdXNlcnMgdG8gY3VzdG9taXplIGl0IHZpYSBuZXh0LmNvbmZpZ1xuICAgICAgICAgICAgaWYgKGNhY2hlRW50cnkuY2FjaGVDb250cm9sICYmICFyZXMuZ2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJykgJiYgIWhlYWRlcnMuZ2V0KCdDYWNoZS1Db250cm9sJykpIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzLnNldCgnQ2FjaGUtQ29udHJvbCcsIGdldENhY2hlQ29udHJvbEhlYWRlcihjYWNoZUVudHJ5LmNhY2hlQ29udHJvbCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYXdhaXQgc2VuZFJlc3BvbnNlKG5vZGVOZXh0UmVxLCBub2RlTmV4dFJlcywgbmV3IFJlc3BvbnNlKGNhY2hlRW50cnkudmFsdWUuYm9keSwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnMsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBjYWNoZUVudHJ5LnZhbHVlLnN0YXR1cyB8fCAyMDBcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9O1xuICAgICAgICAvLyBUT0RPOiBhY3RpdmVTcGFuIGNvZGUgcGF0aCBpcyBmb3Igd2hlbiB3cmFwcGVkIGJ5XG4gICAgICAgIC8vIG5leHQtc2VydmVyIGNhbiBiZSByZW1vdmVkIHdoZW4gdGhpcyBpcyBubyBsb25nZXIgdXNlZFxuICAgICAgICBpZiAoYWN0aXZlU3Bhbikge1xuICAgICAgICAgICAgYXdhaXQgaGFuZGxlUmVzcG9uc2UoYWN0aXZlU3Bhbik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBhd2FpdCB0cmFjZXIud2l0aFByb3BhZ2F0ZWRDb250ZXh0KHJlcS5oZWFkZXJzLCAoKT0+dHJhY2VyLnRyYWNlKEJhc2VTZXJ2ZXJTcGFuLmhhbmRsZVJlcXVlc3QsIHtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbk5hbWU6IGAke21ldGhvZH0gJHtyZXEudXJsfWAsXG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IFNwYW5LaW5kLlNFUlZFUixcbiAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgJ2h0dHAubWV0aG9kJzogbWV0aG9kLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2h0dHAudGFyZ2V0JzogcmVxLnVybFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSwgaGFuZGxlUmVzcG9uc2UpKTtcbiAgICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAvLyBpZiB3ZSBhcmVuJ3Qgd3JhcHBlZCBieSBiYXNlLXNlcnZlciBoYW5kbGUgaGVyZVxuICAgICAgICBpZiAoIWFjdGl2ZVNwYW4gJiYgIShlcnIgaW5zdGFuY2VvZiBOb0ZhbGxiYWNrRXJyb3IpKSB7XG4gICAgICAgICAgICBhd2FpdCByb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVyciwge1xuICAgICAgICAgICAgICAgIHJvdXRlcktpbmQ6ICdBcHAgUm91dGVyJyxcbiAgICAgICAgICAgICAgICByb3V0ZVBhdGg6IG5vcm1hbGl6ZWRTcmNQYWdlLFxuICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JvdXRlJyxcbiAgICAgICAgICAgICAgICByZXZhbGlkYXRlUmVhc29uOiBnZXRSZXZhbGlkYXRlUmVhc29uKHtcbiAgICAgICAgICAgICAgICAgICAgaXNSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICAvLyByZXRocm93IHNvIHRoYXQgd2UgY2FuIGhhbmRsZSBzZXJ2aW5nIGVycm9yIHBhZ2VcbiAgICAgICAgLy8gSWYgdGhpcyBpcyBkdXJpbmcgc3RhdGljIGdlbmVyYXRpb24sIHRocm93IHRoZSBlcnJvciBhZ2Fpbi5cbiAgICAgICAgaWYgKGlzSXNyKSB0aHJvdyBlcnI7XG4gICAgICAgIC8vIE90aGVyd2lzZSwgc2VuZCBhIDUwMCByZXNwb25zZS5cbiAgICAgICAgYXdhaXQgc2VuZFJlc3BvbnNlKG5vZGVOZXh0UmVxLCBub2RlTmV4dFJlcywgbmV3IFJlc3BvbnNlKG51bGwsIHtcbiAgICAgICAgICAgIHN0YXR1czogNTAwXG4gICAgICAgIH0pKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-results%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-results%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-results%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/betexplorer/live-results/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/betexplorer/live-results/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/betexplorer-types */ \"(rsc)/./src/lib/betexplorer-types.ts\");\n\n\n/**\n * API Route para Live Results do BetExplorer\n * Funciona como proxy para contornar CORS e adicionar headers necessários\n */ // Cache simples em memória (em produção, usar Redis ou similar)\nlet cache = {\n    data: null,\n    timestamp: 0\n};\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const eventId = searchParams.get('eventId') // Filtro opcional por evento específico\n        ;\n        // Verificar cache (30 segundos para dados ao vivo)\n        const now = Date.now();\n        const cacheValid = cache.data && now - cache.timestamp < _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.CACHE_TTL_MS;\n        if (cacheValid && !eventId) {\n            console.log('📦 Retornando dados do cache - Live Results');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cache.data,\n                timestamp: new Date().toISOString(),\n                cached: true\n            });\n        }\n        console.log('🔄 Buscando dados do BetExplorer - Live Results');\n        // Construir URL da API\n        let apiUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`;\n        if (eventId) {\n            apiUrl += `?GETeventId=${eventId}`;\n        }\n        // Fazer requisição para BetExplorer\n        const response = await fetch(apiUrl, {\n            method: 'GET',\n            headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.HEADERS,\n            // Adicionar timeout\n            signal: AbortSignal.timeout(10000) // 10 segundos\n        });\n        if (!response.ok) {\n            throw new Error(`BetExplorer API retornou status ${response.status}`);\n        }\n        const rawData = await response.json();\n        // Validar estrutura básica da resposta\n        if (!rawData || typeof rawData !== 'object') {\n            throw new Error('Resposta inválida da API do BetExplorer');\n        }\n        // Processar e filtrar dados\n        const processedData = {\n            events: {},\n            event_stages: rawData.event_stages || {},\n            odds: {\n                live_odds_1x2: rawData.live_odds_1x2 || {},\n                live_odds_ou: rawData.live_odds_ou || {},\n                live_odds_dc: rawData.live_odds_dc || {},\n                live_odds_dnb: rawData.live_odds_dnb || {},\n                live_odds_btts: rawData.live_odds_btts || {},\n                live_odds_ah: rawData.live_odds_ah || {}\n            },\n            bookmakers: rawData.bookmakers || []\n        };\n        // Filtrar apenas eventos de futebol (sport_id = 1)\n        if (rawData.events && typeof rawData.events === 'object') {\n            Object.entries(rawData.events).forEach(([eventId, event])=>{\n                if (event && event.sport_id === _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.SPORT_IDS.FOOTBALL) {\n                    processedData.events[eventId] = {\n                        event_id: eventId,\n                        score: event.score || '0:0',\n                        minute: parseInt(event.minute) || 0,\n                        finished: event.finished === 1 ? 1 : 0,\n                        sport_id: event.sport_id,\n                        event_stage_id: event.event_stage_id || ''\n                    };\n                }\n            });\n        }\n        // Atualizar cache apenas se não foi filtro específico\n        if (!eventId) {\n            cache = {\n                data: processedData,\n                timestamp: now\n            };\n        }\n        console.log(`✅ Dados processados: ${Object.keys(processedData.events).length} eventos de futebol`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: processedData,\n            timestamp: new Date().toISOString(),\n            cached: false,\n            eventsCount: Object.keys(processedData.events).length\n        });\n    } catch (error) {\n        console.error('❌ Erro na API Live Results:', error);\n        // Se houver dados em cache, retornar mesmo com erro\n        if (cache.data) {\n            console.log('⚠️ Retornando dados do cache devido ao erro');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cache.data,\n                timestamp: new Date().toISOString(),\n                cached: true,\n                warning: 'Dados do cache devido a erro na API'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Erro desconhecido',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n// Configurar CORS se necessário\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type'\n        }\n    });\n}\n// Metadados da rota\nconst runtime = 'nodejs';\nconst dynamic = 'force-dynamic' // Sempre executar dinamicamente\n;\nconst revalidate = 0 // Não cachear no Next.js\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/betexplorer/live-results/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/betexplorer-types.ts":
/*!**************************************!*\
  !*** ./src/lib/betexplorer-types.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BETEXPLORER_CONFIG: () => (/* binding */ BETEXPLORER_CONFIG),\n/* harmony export */   MARKET_TYPES: () => (/* binding */ MARKET_TYPES),\n/* harmony export */   SPORT_IDS: () => (/* binding */ SPORT_IDS)\n/* harmony export */ });\n/**\n * Tipos TypeScript para as APIs do BetExplorer\n * Baseado na documentação completa das APIs descobertas\n */ // ============================================================================\n// TIPOS BASE\n// ============================================================================\n// ============================================================================\n// CONFIGURAÇÕES E CONSTANTES\n// ============================================================================\nconst BETEXPLORER_CONFIG = {\n    BASE_URL: 'https://www.betexplorer.com',\n    RATE_LIMIT_MS: 2000,\n    CACHE_TTL_MS: 30000,\n    HEADERS: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Accept': 'application/json, text/html, */*',\n        'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Referer': 'https://www.betexplorer.com'\n    }\n};\nconst SPORT_IDS = {\n    FOOTBALL: 1,\n    TENNIS: 2,\n    BASKETBALL: 3,\n    HOCKEY: 4\n};\nconst MARKET_TYPES = {\n    ODDS_1X2: '1x2',\n    OVER_UNDER: 'ou',\n    ASIAN_HANDICAP: 'ah',\n    DRAW_NO_BET: 'dnb',\n    DOUBLE_CHANCE: 'dc',\n    BOTH_TEAMS_TO_SCORE: 'btts'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/betexplorer-types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-results%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-results%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-results%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();