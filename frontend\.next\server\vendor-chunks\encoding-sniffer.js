"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encoding-sniffer";
exports.ids = ["vendor-chunks/encoding-sniffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeStream: () => (/* binding */ DecodeStream),\n/* harmony export */   decodeBuffer: () => (/* binding */ decodeBuffer),\n/* harmony export */   getEncoding: () => (/* reexport safe */ _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var iconv_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\n/* harmony import */ var _sniffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sniffer.js */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\");\n\n\n\n/**\n * Sniff the encoding of a buffer, then decode it.\n *\n * @param buffer Buffer to be decoded\n * @param options Options for the sniffer\n * @returns The decoded buffer\n */\nfunction decodeBuffer(buffer, options = {}) {\n    return iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decode(buffer, (0,_sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)(buffer, options));\n}\n/**\n * Decodes a stream of buffers into a stream of strings.\n *\n * Reads the first 1024 bytes and passes them to the sniffer. Once an encoding\n * has been determined, it passes all data to iconv-lite's stream and outputs\n * the results.\n */\nclass DecodeStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Transform {\n    constructor(options) {\n        var _a;\n        super({ decodeStrings: false, encoding: \"utf-8\" });\n        this.buffers = [];\n        /** The iconv decode stream. If it is set, we have read more than `options.maxBytes` bytes. */\n        this.iconv = null;\n        this.readBytes = 0;\n        this.sniffer = new _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.Sniffer(options);\n        this.maxBytes = (_a = options === null || options === void 0 ? void 0 : options.maxBytes) !== null && _a !== void 0 ? _a : 1024;\n    }\n    _transform(chunk, _encoding, callback) {\n        if (this.readBytes < this.maxBytes) {\n            this.sniffer.write(chunk);\n            this.readBytes += chunk.length;\n            if (this.readBytes < this.maxBytes) {\n                this.buffers.push(chunk);\n                callback();\n                return;\n            }\n        }\n        this.getIconvStream().write(chunk, callback);\n    }\n    getIconvStream() {\n        if (this.iconv) {\n            return this.iconv;\n        }\n        const stream = iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decodeStream(this.sniffer.encoding);\n        stream.on(\"data\", (chunk) => this.push(chunk, \"utf-8\"));\n        stream.on(\"end\", () => this.push(null));\n        this.iconv = stream;\n        for (const buffer of this.buffers) {\n            stream.write(buffer);\n        }\n        this.buffers.length = 0;\n        return stream;\n    }\n    _flush(callback) {\n        this.getIconvStream().end(callback);\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZW5jb2Rpbmctc25pZmZlci9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBd0M7QUFDVDtBQUNxQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDBDQUEwQztBQUNqRCxXQUFXLDhDQUFZLFNBQVMsd0RBQVc7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDJCQUEyQixrREFBUztBQUMzQztBQUNBO0FBQ0EsZ0JBQWdCLHlDQUF5QztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnREFBTztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsb0RBQWtCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQzNDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZW5jb2Rpbmctc25pZmZlclxcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUcmFuc2Zvcm0gfSBmcm9tIFwibm9kZTpzdHJlYW1cIjtcbmltcG9ydCBpY29udiBmcm9tIFwiaWNvbnYtbGl0ZVwiO1xuaW1wb3J0IHsgU25pZmZlciwgZ2V0RW5jb2RpbmcgfSBmcm9tIFwiLi9zbmlmZmVyLmpzXCI7XG4vKipcbiAqIFNuaWZmIHRoZSBlbmNvZGluZyBvZiBhIGJ1ZmZlciwgdGhlbiBkZWNvZGUgaXQuXG4gKlxuICogQHBhcmFtIGJ1ZmZlciBCdWZmZXIgdG8gYmUgZGVjb2RlZFxuICogQHBhcmFtIG9wdGlvbnMgT3B0aW9ucyBmb3IgdGhlIHNuaWZmZXJcbiAqIEByZXR1cm5zIFRoZSBkZWNvZGVkIGJ1ZmZlclxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVjb2RlQnVmZmVyKGJ1ZmZlciwgb3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIGljb252LmRlY29kZShidWZmZXIsIGdldEVuY29kaW5nKGJ1ZmZlciwgb3B0aW9ucykpO1xufVxuLyoqXG4gKiBEZWNvZGVzIGEgc3RyZWFtIG9mIGJ1ZmZlcnMgaW50byBhIHN0cmVhbSBvZiBzdHJpbmdzLlxuICpcbiAqIFJlYWRzIHRoZSBmaXJzdCAxMDI0IGJ5dGVzIGFuZCBwYXNzZXMgdGhlbSB0byB0aGUgc25pZmZlci4gT25jZSBhbiBlbmNvZGluZ1xuICogaGFzIGJlZW4gZGV0ZXJtaW5lZCwgaXQgcGFzc2VzIGFsbCBkYXRhIHRvIGljb252LWxpdGUncyBzdHJlYW0gYW5kIG91dHB1dHNcbiAqIHRoZSByZXN1bHRzLlxuICovXG5leHBvcnQgY2xhc3MgRGVjb2RlU3RyZWFtIGV4dGVuZHMgVHJhbnNmb3JtIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgc3VwZXIoeyBkZWNvZGVTdHJpbmdzOiBmYWxzZSwgZW5jb2Rpbmc6IFwidXRmLThcIiB9KTtcbiAgICAgICAgdGhpcy5idWZmZXJzID0gW107XG4gICAgICAgIC8qKiBUaGUgaWNvbnYgZGVjb2RlIHN0cmVhbS4gSWYgaXQgaXMgc2V0LCB3ZSBoYXZlIHJlYWQgbW9yZSB0aGFuIGBvcHRpb25zLm1heEJ5dGVzYCBieXRlcy4gKi9cbiAgICAgICAgdGhpcy5pY29udiA9IG51bGw7XG4gICAgICAgIHRoaXMucmVhZEJ5dGVzID0gMDtcbiAgICAgICAgdGhpcy5zbmlmZmVyID0gbmV3IFNuaWZmZXIob3B0aW9ucyk7XG4gICAgICAgIHRoaXMubWF4Qnl0ZXMgPSAoX2EgPSBvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMubWF4Qnl0ZXMpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IDEwMjQ7XG4gICAgfVxuICAgIF90cmFuc2Zvcm0oY2h1bmssIF9lbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgaWYgKHRoaXMucmVhZEJ5dGVzIDwgdGhpcy5tYXhCeXRlcykge1xuICAgICAgICAgICAgdGhpcy5zbmlmZmVyLndyaXRlKGNodW5rKTtcbiAgICAgICAgICAgIHRoaXMucmVhZEJ5dGVzICs9IGNodW5rLmxlbmd0aDtcbiAgICAgICAgICAgIGlmICh0aGlzLnJlYWRCeXRlcyA8IHRoaXMubWF4Qnl0ZXMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmJ1ZmZlcnMucHVzaChjaHVuayk7XG4gICAgICAgICAgICAgICAgY2FsbGJhY2soKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5nZXRJY29udlN0cmVhbSgpLndyaXRlKGNodW5rLCBjYWxsYmFjayk7XG4gICAgfVxuICAgIGdldEljb252U3RyZWFtKCkge1xuICAgICAgICBpZiAodGhpcy5pY29udikge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuaWNvbnY7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc3RyZWFtID0gaWNvbnYuZGVjb2RlU3RyZWFtKHRoaXMuc25pZmZlci5lbmNvZGluZyk7XG4gICAgICAgIHN0cmVhbS5vbihcImRhdGFcIiwgKGNodW5rKSA9PiB0aGlzLnB1c2goY2h1bmssIFwidXRmLThcIikpO1xuICAgICAgICBzdHJlYW0ub24oXCJlbmRcIiwgKCkgPT4gdGhpcy5wdXNoKG51bGwpKTtcbiAgICAgICAgdGhpcy5pY29udiA9IHN0cmVhbTtcbiAgICAgICAgZm9yIChjb25zdCBidWZmZXIgb2YgdGhpcy5idWZmZXJzKSB7XG4gICAgICAgICAgICBzdHJlYW0ud3JpdGUoYnVmZmVyKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmJ1ZmZlcnMubGVuZ3RoID0gMDtcbiAgICAgICAgcmV0dXJuIHN0cmVhbTtcbiAgICB9XG4gICAgX2ZsdXNoKGNhbGxiYWNrKSB7XG4gICAgICAgIHRoaXMuZ2V0SWNvbnZTdHJlYW0oKS5lbmQoY2FsbGJhY2spO1xuICAgIH1cbn1cbmV4cG9ydCB7IGdldEVuY29kaW5nIH0gZnJvbSBcIi4vc25pZmZlci5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js":
/*!***********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/sniffer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResultType: () => (/* binding */ ResultType),\n/* harmony export */   STRINGS: () => (/* binding */ STRINGS),\n/* harmony export */   Sniffer: () => (/* binding */ Sniffer),\n/* harmony export */   getEncoding: () => (/* binding */ getEncoding)\n/* harmony export */ });\n/* harmony import */ var whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! whatwg-encoding */ \"(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js\");\n\n// https://html.spec.whatwg.org/multipage/syntax.html#prescan-a-byte-stream-to-determine-its-encoding\nvar State;\n(function (State) {\n    // Before anything starts; can be any of BOM, UTF-16 XML declarations or meta tags\n    State[State[\"Begin\"] = 0] = \"Begin\";\n    // Inside of a BOM\n    State[State[\"BOM16BE\"] = 1] = \"BOM16BE\";\n    State[State[\"BOM16LE\"] = 2] = \"BOM16LE\";\n    State[State[\"BOM8\"] = 3] = \"BOM8\";\n    // XML prefix\n    State[State[\"UTF16LE_XML_PREFIX\"] = 4] = \"UTF16LE_XML_PREFIX\";\n    State[State[\"BeginLT\"] = 5] = \"BeginLT\";\n    State[State[\"UTF16BE_XML_PREFIX\"] = 6] = \"UTF16BE_XML_PREFIX\";\n    // Waiting for opening `<`\n    State[State[\"BeforeTag\"] = 7] = \"BeforeTag\";\n    // After the opening `<`\n    State[State[\"BeforeTagName\"] = 8] = \"BeforeTagName\";\n    // After `</`\n    State[State[\"BeforeCloseTagName\"] = 9] = \"BeforeCloseTagName\";\n    // Beginning of a comment\n    State[State[\"CommentStart\"] = 10] = \"CommentStart\";\n    // End of a comment\n    State[State[\"CommentEnd\"] = 11] = \"CommentEnd\";\n    // A tag name that could be `meta`\n    State[State[\"TagNameMeta\"] = 12] = \"TagNameMeta\";\n    // A tag name that is not `meta`\n    State[State[\"TagNameOther\"] = 13] = \"TagNameOther\";\n    // XML declaration\n    State[State[\"XMLDeclaration\"] = 14] = \"XMLDeclaration\";\n    State[State[\"XMLDeclarationBeforeEncoding\"] = 15] = \"XMLDeclarationBeforeEncoding\";\n    State[State[\"XMLDeclarationAfterEncoding\"] = 16] = \"XMLDeclarationAfterEncoding\";\n    State[State[\"XMLDeclarationBeforeValue\"] = 17] = \"XMLDeclarationBeforeValue\";\n    State[State[\"XMLDeclarationValue\"] = 18] = \"XMLDeclarationValue\";\n    // Anything that looks like a tag, but doesn't fit in the above categories\n    State[State[\"WeirdTag\"] = 19] = \"WeirdTag\";\n    State[State[\"BeforeAttribute\"] = 20] = \"BeforeAttribute\";\n    /*\n     * Attributes in meta tag — we compare them to our set here, and back out\n     * We care about four attributes: http-equiv, content-type, content, charset\n     */\n    State[State[\"MetaAttribHttpEquiv\"] = 21] = \"MetaAttribHttpEquiv\";\n    // The value has to be `content-type`\n    State[State[\"MetaAttribHttpEquivValue\"] = 22] = \"MetaAttribHttpEquivValue\";\n    State[State[\"MetaAttribC\"] = 23] = \"MetaAttribC\";\n    State[State[\"MetaAttribContent\"] = 24] = \"MetaAttribContent\";\n    State[State[\"MetaAttribCharset\"] = 25] = \"MetaAttribCharset\";\n    // Waiting for whitespace\n    State[State[\"MetaAttribAfterName\"] = 26] = \"MetaAttribAfterName\";\n    State[State[\"MetaContentValueQuotedBeforeEncoding\"] = 27] = \"MetaContentValueQuotedBeforeEncoding\";\n    State[State[\"MetaContentValueQuotedAfterEncoding\"] = 28] = \"MetaContentValueQuotedAfterEncoding\";\n    State[State[\"MetaContentValueQuotedBeforeValue\"] = 29] = \"MetaContentValueQuotedBeforeValue\";\n    State[State[\"MetaContentValueQuotedValueQuoted\"] = 30] = \"MetaContentValueQuotedValueQuoted\";\n    State[State[\"MetaContentValueQuotedValueUnquoted\"] = 31] = \"MetaContentValueQuotedValueUnquoted\";\n    State[State[\"MetaContentValueUnquotedBeforeEncoding\"] = 32] = \"MetaContentValueUnquotedBeforeEncoding\";\n    State[State[\"MetaContentValueUnquotedBeforeValue\"] = 33] = \"MetaContentValueUnquotedBeforeValue\";\n    State[State[\"MetaContentValueUnquotedValueQuoted\"] = 34] = \"MetaContentValueUnquotedValueQuoted\";\n    State[State[\"MetaContentValueUnquotedValueUnquoted\"] = 35] = \"MetaContentValueUnquotedValueUnquoted\";\n    State[State[\"AnyAttribName\"] = 36] = \"AnyAttribName\";\n    // After the name of an attribute, before the equals sign\n    State[State[\"AfterAttributeName\"] = 37] = \"AfterAttributeName\";\n    // After `=`\n    State[State[\"BeforeAttributeValue\"] = 38] = \"BeforeAttributeValue\";\n    State[State[\"AttributeValueQuoted\"] = 39] = \"AttributeValueQuoted\";\n    State[State[\"AttributeValueUnquoted\"] = 40] = \"AttributeValueUnquoted\";\n})(State || (State = {}));\nvar ResultType;\n(function (ResultType) {\n    // Byte order mark\n    ResultType[ResultType[\"BOM\"] = 0] = \"BOM\";\n    // User- or transport layer-defined\n    ResultType[ResultType[\"PASSED\"] = 1] = \"PASSED\";\n    // XML prefixes\n    ResultType[ResultType[\"XML_PREFIX\"] = 2] = \"XML_PREFIX\";\n    // Meta tag\n    ResultType[ResultType[\"META_TAG\"] = 3] = \"META_TAG\";\n    // XML encoding\n    ResultType[ResultType[\"XML_ENCODING\"] = 4] = \"XML_ENCODING\";\n    // Default\n    ResultType[ResultType[\"DEFAULT\"] = 5] = \"DEFAULT\";\n})(ResultType || (ResultType = {}));\nvar AttribType;\n(function (AttribType) {\n    AttribType[AttribType[\"None\"] = 0] = \"None\";\n    AttribType[AttribType[\"HttpEquiv\"] = 1] = \"HttpEquiv\";\n    AttribType[AttribType[\"Content\"] = 2] = \"Content\";\n    AttribType[AttribType[\"Charset\"] = 3] = \"Charset\";\n})(AttribType || (AttribType = {}));\nvar Chars;\n(function (Chars) {\n    Chars[Chars[\"NIL\"] = 0] = \"NIL\";\n    Chars[Chars[\"TAB\"] = 9] = \"TAB\";\n    Chars[Chars[\"LF\"] = 10] = \"LF\";\n    Chars[Chars[\"CR\"] = 13] = \"CR\";\n    Chars[Chars[\"SPACE\"] = 32] = \"SPACE\";\n    Chars[Chars[\"EXCLAMATION\"] = 33] = \"EXCLAMATION\";\n    Chars[Chars[\"DQUOTE\"] = 34] = \"DQUOTE\";\n    Chars[Chars[\"SQUOTE\"] = 39] = \"SQUOTE\";\n    Chars[Chars[\"DASH\"] = 45] = \"DASH\";\n    Chars[Chars[\"SLASH\"] = 47] = \"SLASH\";\n    Chars[Chars[\"SEMICOLON\"] = 59] = \"SEMICOLON\";\n    Chars[Chars[\"LT\"] = 60] = \"LT\";\n    Chars[Chars[\"EQUALS\"] = 61] = \"EQUALS\";\n    Chars[Chars[\"GT\"] = 62] = \"GT\";\n    Chars[Chars[\"QUESTION\"] = 63] = \"QUESTION\";\n    Chars[Chars[\"UpperA\"] = 65] = \"UpperA\";\n    Chars[Chars[\"UpperZ\"] = 90] = \"UpperZ\";\n    Chars[Chars[\"LowerA\"] = 97] = \"LowerA\";\n    Chars[Chars[\"LowerZ\"] = 122] = \"LowerZ\";\n})(Chars || (Chars = {}));\nconst SPACE_CHARACTERS = new Set([Chars.SPACE, Chars.LF, Chars.CR, Chars.TAB]);\nconst END_OF_UNQUOTED_ATTRIBUTE_VALUE = new Set([\n    Chars.SPACE,\n    Chars.LF,\n    Chars.CR,\n    Chars.TAB,\n    Chars.GT,\n]);\nfunction toUint8Array(str) {\n    const arr = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; i++) {\n        arr[i] = str.charCodeAt(i);\n    }\n    return arr;\n}\nconst STRINGS = {\n    UTF8_BOM: new Uint8Array([0xef, 0xbb, 0xbf]),\n    UTF16LE_BOM: new Uint8Array([0xff, 0xfe]),\n    UTF16BE_BOM: new Uint8Array([0xfe, 0xff]),\n    UTF16LE_XML_PREFIX: new Uint8Array([0x3c, 0x0, 0x3f, 0x0, 0x78, 0x0]),\n    UTF16BE_XML_PREFIX: new Uint8Array([0x0, 0x3c, 0x0, 0x3f, 0x0, 0x78]),\n    XML_DECLARATION: toUint8Array(\"<?xml\"),\n    ENCODING: toUint8Array(\"encoding\"),\n    META: toUint8Array(\"meta\"),\n    HTTP_EQUIV: toUint8Array(\"http-equiv\"),\n    CONTENT: toUint8Array(\"content\"),\n    CONTENT_TYPE: toUint8Array(\"content-type\"),\n    CHARSET: toUint8Array(\"charset\"),\n    COMMENT_START: toUint8Array(\"<!--\"),\n    COMMENT_END: toUint8Array(\"-->\"),\n};\nfunction isAsciiAlpha(c) {\n    return ((c >= Chars.UpperA && c <= Chars.UpperZ) ||\n        (c >= Chars.LowerA && c <= Chars.LowerZ));\n}\nfunction isQuote(c) {\n    return c === Chars.DQUOTE || c === Chars.SQUOTE;\n}\nclass Sniffer {\n    setResult(label, type) {\n        if (this.resultType === ResultType.DEFAULT || this.resultType > type) {\n            const encoding = (0,whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__.labelToName)(label);\n            if (encoding) {\n                this.encoding =\n                    // Check if we are in a meta tag and the encoding is `x-user-defined`\n                    type === ResultType.META_TAG &&\n                        encoding === \"x-user-defined\"\n                        ? \"windows-1252\"\n                        : // Check if we are in a meta tag or xml declaration, and the encoding is UTF-16\n                            (type === ResultType.META_TAG ||\n                                type === ResultType.XML_ENCODING) &&\n                                (encoding === \"UTF-16LE\" || encoding === \"UTF-16BE\")\n                                ? \"UTF-8\"\n                                : encoding;\n                this.resultType = type;\n            }\n        }\n    }\n    constructor({ maxBytes = 1024, userEncoding, transportLayerEncodingLabel, defaultEncoding, } = {}) {\n        /** The offset of the previous buffers. */\n        this.offset = 0;\n        this.state = State.Begin;\n        this.sectionIndex = 0;\n        this.attribType = AttribType.None;\n        /**\n         * Indicates if the `http-equiv` is `content-type`.\n         *\n         * Initially `null`, a boolean when a value is found.\n         */\n        this.gotPragma = null;\n        this.needsPragma = null;\n        this.inMetaTag = false;\n        this.encoding = \"windows-1252\";\n        this.resultType = ResultType.DEFAULT;\n        this.quoteCharacter = 0;\n        this.attributeValue = [];\n        this.maxBytes = maxBytes;\n        if (userEncoding) {\n            this.setResult(userEncoding, ResultType.PASSED);\n        }\n        if (transportLayerEncodingLabel) {\n            this.setResult(transportLayerEncodingLabel, ResultType.PASSED);\n        }\n        if (defaultEncoding) {\n            this.setResult(defaultEncoding, ResultType.DEFAULT);\n        }\n    }\n    stateBegin(c) {\n        switch (c) {\n            case STRINGS.UTF16BE_BOM[0]: {\n                this.state = State.BOM16BE;\n                break;\n            }\n            case STRINGS.UTF16LE_BOM[0]: {\n                this.state = State.BOM16LE;\n                break;\n            }\n            case STRINGS.UTF8_BOM[0]: {\n                this.sectionIndex = 1;\n                this.state = State.BOM8;\n                break;\n            }\n            case Chars.NIL: {\n                this.state = State.UTF16BE_XML_PREFIX;\n                this.sectionIndex = 1;\n                break;\n            }\n            case Chars.LT: {\n                this.state = State.BeginLT;\n                break;\n            }\n            default: {\n                this.state = State.BeforeTag;\n            }\n        }\n    }\n    stateBeginLT(c) {\n        if (c === Chars.NIL) {\n            this.state = State.UTF16LE_XML_PREFIX;\n            this.sectionIndex = 2;\n        }\n        else if (c === Chars.QUESTION) {\n            this.state = State.XMLDeclaration;\n            this.sectionIndex = 2;\n        }\n        else {\n            this.state = State.BeforeTagName;\n            this.stateBeforeTagName(c);\n        }\n    }\n    stateUTF16BE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16BE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16BE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16be\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateUTF16LE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16LE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16LE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16le\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16LE(c) {\n        if (c === STRINGS.UTF16LE_BOM[1]) {\n            this.setResult(\"utf-16le\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16BE(c) {\n        if (c === STRINGS.UTF16BE_BOM[1]) {\n            this.setResult(\"utf-16be\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM8(c) {\n        if (this.advanceSection(STRINGS.UTF8_BOM, c) &&\n            this.sectionIndex === STRINGS.UTF8_BOM.length) {\n            this.setResult(\"utf-8\", ResultType.BOM);\n        }\n    }\n    stateBeforeTag(c) {\n        if (c === Chars.LT) {\n            this.state = State.BeforeTagName;\n            this.inMetaTag = false;\n        }\n    }\n    /**\n     * We have seen a `<`, and now have to figure out what to do.\n     *\n     * Options:\n     *  - `<meta`\n     *  - Any other tag\n     *  - A closing tag\n     *  - `<!--`\n     *  - An XML declaration\n     *\n     */\n    stateBeforeTagName(c) {\n        if (isAsciiAlpha(c)) {\n            if ((c | 0x20) === STRINGS.META[0]) {\n                this.sectionIndex = 1;\n                this.state = State.TagNameMeta;\n            }\n            else {\n                this.state = State.TagNameOther;\n            }\n        }\n        else\n            switch (c) {\n                case Chars.SLASH: {\n                    this.state = State.BeforeCloseTagName;\n                    break;\n                }\n                case Chars.EXCLAMATION: {\n                    this.state = State.CommentStart;\n                    this.sectionIndex = 2;\n                    break;\n                }\n                case Chars.QUESTION: {\n                    this.state = State.WeirdTag;\n                    break;\n                }\n                default: {\n                    this.state = State.BeforeTag;\n                    this.stateBeforeTag(c);\n                }\n            }\n    }\n    stateBeforeCloseTagName(c) {\n        this.state = isAsciiAlpha(c)\n            ? // Switch to `TagNameOther`; the HTML spec allows attributes here as well.\n                State.TagNameOther\n            : State.WeirdTag;\n    }\n    stateCommentStart(c) {\n        if (this.advanceSection(STRINGS.COMMENT_START, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_START.length) {\n                this.state = State.CommentEnd;\n                // The -- of the comment start can be part of the end.\n                this.sectionIndex = 2;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateCommentEnd(c) {\n        if (this.advanceSection(STRINGS.COMMENT_END, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_END.length) {\n                this.state = State.BeforeTag;\n            }\n        }\n        else if (c === Chars.DASH) {\n            /*\n             * If we are here, we know we expected a `>` above.\n             * Set this to 2, to support many dashes before the closing `>`.\n             */\n            this.sectionIndex = 2;\n        }\n    }\n    /**\n     * Any section starting with `<!`, `<?`, `</`, without being a closing tag or comment.\n     */\n    stateWeirdTag(c) {\n        if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    /**\n     * Advances the section, ignoring upper/lower case.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSectionIC(section, c) {\n        return this.advanceSection(section, c | 0x20);\n    }\n    /**\n     * Advances the section.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSection(section, c) {\n        if (section[this.sectionIndex] === c) {\n            this.sectionIndex++;\n            return true;\n        }\n        this.sectionIndex = 0;\n        return false;\n    }\n    stateTagNameMeta(c) {\n        if (this.sectionIndex < STRINGS.META.length) {\n            if (this.advanceSectionIC(STRINGS.META, c)) {\n                return;\n            }\n        }\n        else if (SPACE_CHARACTERS.has(c)) {\n            this.inMetaTag = true;\n            this.gotPragma = null;\n            this.needsPragma = null;\n            this.state = State.BeforeAttribute;\n            return;\n        }\n        this.state = State.TagNameOther;\n        // Reconsume in case there is a `>`.\n        this.stateTagNameOther(c);\n    }\n    stateTagNameOther(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    stateBeforeAttribute(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (this.inMetaTag) {\n            const lower = c | 0x20;\n            if (lower === STRINGS.HTTP_EQUIV[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribHttpEquiv;\n                return;\n            }\n            else if (lower === STRINGS.CHARSET[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribC;\n                return;\n            }\n        }\n        this.state =\n            c === Chars.SLASH || c === Chars.GT\n                ? State.BeforeTag\n                : State.AnyAttribName;\n    }\n    handleMetaAttrib(c, section, type) {\n        if (this.advanceSectionIC(section, c)) {\n            if (this.sectionIndex === section.length) {\n                this.attribType = type;\n                this.state = State.MetaAttribAfterName;\n            }\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribHttpEquiv(c) {\n        this.handleMetaAttrib(c, STRINGS.HTTP_EQUIV, AttribType.HttpEquiv);\n    }\n    stateMetaAttribC(c) {\n        const lower = c | 0x20;\n        if (lower === STRINGS.CHARSET[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribCharset;\n        }\n        else if (lower === STRINGS.CONTENT[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribContent;\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribCharset(c) {\n        this.handleMetaAttrib(c, STRINGS.CHARSET, AttribType.Charset);\n    }\n    stateMetaAttribContent(c) {\n        this.handleMetaAttrib(c, STRINGS.CONTENT, AttribType.Content);\n    }\n    stateMetaAttribAfterName(c) {\n        if (SPACE_CHARACTERS.has(c) || c === Chars.EQUALS) {\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateAnyAttribName(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.attribType = AttribType.None;\n            this.state = State.AfterAttributeName;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else {\n            this.state = State.BeforeAttribute;\n            this.stateBeforeAttribute(c);\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        this.attributeValue.length = 0;\n        this.sectionIndex = 0;\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state =\n                this.attribType === AttribType.Content\n                    ? State.MetaContentValueQuotedBeforeEncoding\n                    : this.attribType === AttribType.HttpEquiv\n                        ? State.MetaAttribHttpEquivValue\n                        : State.AttributeValueQuoted;\n        }\n        else if (this.attribType === AttribType.Content) {\n            this.state = State.MetaContentValueUnquotedBeforeEncoding;\n            this.stateMetaContentValueUnquotedBeforeEncoding(c);\n        }\n        else if (this.attribType === AttribType.HttpEquiv) {\n            // We use `quoteCharacter = 0` to signify that the value is unquoted.\n            this.quoteCharacter = 0;\n            this.sectionIndex = 0;\n            this.state = State.MetaAttribHttpEquivValue;\n            this.stateMetaAttribHttpEquivValue(c);\n        }\n        else {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n    }\n    // The value has to be `content-type`\n    stateMetaAttribHttpEquivValue(c) {\n        if (this.sectionIndex === STRINGS.CONTENT_TYPE.length) {\n            if (this.quoteCharacter === 0\n                ? END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)\n                : c === this.quoteCharacter) {\n                if (this.needsPragma !== null) {\n                    this.setResult(this.needsPragma, ResultType.META_TAG);\n                }\n                else if (this.gotPragma === null) {\n                    this.gotPragma = true;\n                }\n                this.state = State.BeforeAttribute;\n                return;\n            }\n        }\n        else if (this.advanceSectionIC(STRINGS.CONTENT_TYPE, c)) {\n            return;\n        }\n        this.gotPragma = false;\n        if (this.quoteCharacter === 0) {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n    }\n    handleMetaContentValue() {\n        if (this.attributeValue.length === 0)\n            return;\n        const encoding = String.fromCharCode(...this.attributeValue);\n        if (this.gotPragma) {\n            this.setResult(encoding, ResultType.META_TAG);\n        }\n        else if (this.needsPragma === null) {\n            // Don't override a previous result.\n            this.needsPragma = encoding;\n        }\n        this.attributeValue.length = 0;\n    }\n    handleAttributeValue() {\n        if (this.attribType === AttribType.Charset) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.META_TAG);\n        }\n    }\n    stateAttributeValueUnquoted(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.handleAttributeValue();\n            this.state = State.BeforeTag;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    findMetaContentEncoding(c) {\n        if (this.advanceSectionIC(STRINGS.CHARSET, c)) {\n            if (this.sectionIndex === STRINGS.CHARSET.length) {\n                return true;\n            }\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.CHARSET[0]);\n        }\n        return false;\n    }\n    stateMetaContentValueUnquotedBeforeEncoding(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (this.sectionIndex === STRINGS.CHARSET.length) {\n            if (c === Chars.EQUALS) {\n                this.state = State.MetaContentValueUnquotedBeforeValue;\n            }\n        }\n        else {\n            this.findMetaContentEncoding(c);\n        }\n    }\n    stateMetaContentValueUnquotedBeforeValue(c) {\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state = State.MetaContentValueUnquotedValueQuoted;\n        }\n        else if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Can't have spaces here, as it would no longer be part of the attribute value.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.MetaContentValueUnquotedValueUnquoted;\n            this.stateMetaContentValueUnquotedValueUnquoted(c);\n        }\n    }\n    stateMetaContentValueUnquotedValueQuoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Quotes weren't matched, so we're done.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (c === this.quoteCharacter) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueUnquotedValueUnquoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueUnquoted(c) {\n        if (isQuote(c) || SPACE_CHARACTERS.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            // We are done with the value, but might not be at the end of the attribute\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueQuoted(c) {\n        if (isQuote(c)) {\n            // We have reached the end of our value.\n            if (c !== this.quoteCharacter) {\n                // Only handle the value if inner quotes were matched.\n                this.handleMetaContentValue();\n            }\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedBeforeEncoding(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (this.findMetaContentEncoding(c)) {\n            this.state = State.MetaContentValueQuotedAfterEncoding;\n        }\n    }\n    stateMetaContentValueQuotedAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.MetaContentValueQuotedBeforeValue;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            // Look for the next encoding\n            this.state = State.MetaContentValueQuotedBeforeEncoding;\n            this.stateMetaContentValueQuotedBeforeEncoding(c);\n        }\n    }\n    stateMetaContentValueQuotedBeforeValue(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (isQuote(c)) {\n            this.state = State.MetaContentValueQuotedValueQuoted;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            this.state = State.MetaContentValueQuotedValueUnquoted;\n            this.stateMetaContentValueQuotedValueUnquoted(c);\n        }\n    }\n    stateAttributeValueQuoted(c) {\n        if (c === this.quoteCharacter) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    // Read STRINGS.XML_DECLARATION\n    stateXMLDeclaration(c) {\n        if (this.advanceSection(STRINGS.XML_DECLARATION, c)) {\n            if (this.sectionIndex === STRINGS.XML_DECLARATION.length) {\n                this.sectionIndex = 0;\n                this.state = State.XMLDeclarationBeforeEncoding;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n        }\n    }\n    stateXMLDeclarationBeforeEncoding(c) {\n        if (this.advanceSection(STRINGS.ENCODING, c)) {\n            if (this.sectionIndex === STRINGS.ENCODING.length) {\n                this.state = State.XMLDeclarationAfterEncoding;\n            }\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.ENCODING[0]);\n        }\n    }\n    stateXMLDeclarationAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.XMLDeclarationBeforeValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationBeforeValue(c) {\n        if (isQuote(c)) {\n            this.attributeValue.length = 0;\n            this.state = State.XMLDeclarationValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationValue(c) {\n        if (isQuote(c)) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.XML_ENCODING);\n            this.state = State.WeirdTag;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c <= Chars.SPACE) {\n            this.state = State.WeirdTag;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    write(buffer) {\n        let index = 0;\n        for (; index < buffer.length && this.offset + index < this.maxBytes; index++) {\n            const c = buffer[index];\n            switch (this.state) {\n                case State.Begin: {\n                    this.stateBegin(c);\n                    break;\n                }\n                case State.BOM16BE: {\n                    this.stateBOM16BE(c);\n                    break;\n                }\n                case State.BOM16LE: {\n                    this.stateBOM16LE(c);\n                    break;\n                }\n                case State.BOM8: {\n                    this.stateBOM8(c);\n                    break;\n                }\n                case State.UTF16LE_XML_PREFIX: {\n                    this.stateUTF16LE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeginLT: {\n                    this.stateBeginLT(c);\n                    break;\n                }\n                case State.UTF16BE_XML_PREFIX: {\n                    this.stateUTF16BE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeforeTag: {\n                    // Optimization: Skip all characters until we find a `<`\n                    const idx = buffer.indexOf(Chars.LT, index);\n                    if (idx === -1) {\n                        // We are done with this buffer. Stay in the state and try on the next one.\n                        index = buffer.length;\n                    }\n                    else {\n                        index = idx;\n                        this.stateBeforeTag(Chars.LT);\n                    }\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.BeforeCloseTagName: {\n                    this.stateBeforeCloseTagName(c);\n                    break;\n                }\n                case State.CommentStart: {\n                    this.stateCommentStart(c);\n                    break;\n                }\n                case State.CommentEnd: {\n                    this.stateCommentEnd(c);\n                    break;\n                }\n                case State.TagNameMeta: {\n                    this.stateTagNameMeta(c);\n                    break;\n                }\n                case State.TagNameOther: {\n                    this.stateTagNameOther(c);\n                    break;\n                }\n                case State.XMLDeclaration: {\n                    this.stateXMLDeclaration(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeEncoding: {\n                    this.stateXMLDeclarationBeforeEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationAfterEncoding: {\n                    this.stateXMLDeclarationAfterEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeValue: {\n                    this.stateXMLDeclarationBeforeValue(c);\n                    break;\n                }\n                case State.XMLDeclarationValue: {\n                    this.stateXMLDeclarationValue(c);\n                    break;\n                }\n                case State.WeirdTag: {\n                    this.stateWeirdTag(c);\n                    break;\n                }\n                case State.BeforeAttribute: {\n                    this.stateBeforeAttribute(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquiv: {\n                    this.stateMetaAttribHttpEquiv(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquivValue: {\n                    this.stateMetaAttribHttpEquivValue(c);\n                    break;\n                }\n                case State.MetaAttribC: {\n                    this.stateMetaAttribC(c);\n                    break;\n                }\n                case State.MetaAttribContent: {\n                    this.stateMetaAttribContent(c);\n                    break;\n                }\n                case State.MetaAttribCharset: {\n                    this.stateMetaAttribCharset(c);\n                    break;\n                }\n                case State.MetaAttribAfterName: {\n                    this.stateMetaAttribAfterName(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeEncoding: {\n                    this.stateMetaContentValueQuotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedAfterEncoding: {\n                    this.stateMetaContentValueQuotedAfterEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeValue: {\n                    this.stateMetaContentValueQuotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueQuoted: {\n                    this.stateMetaContentValueQuotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueUnquoted: {\n                    this.stateMetaContentValueQuotedValueUnquoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeEncoding: {\n                    this.stateMetaContentValueUnquotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeValue: {\n                    this.stateMetaContentValueUnquotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueQuoted: {\n                    this.stateMetaContentValueUnquotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueUnquoted: {\n                    this.stateMetaContentValueUnquotedValueUnquoted(c);\n                    break;\n                }\n                case State.AnyAttribName: {\n                    this.stateAnyAttribName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.AttributeValueQuoted: {\n                    this.stateAttributeValueQuoted(c);\n                    break;\n                }\n                case State.AttributeValueUnquoted: {\n                    this.stateAttributeValueUnquoted(c);\n                    break;\n                }\n            }\n        }\n        this.offset += index;\n    }\n}\n/** Get the encoding for the passed buffer. */\nfunction getEncoding(buffer, options) {\n    const sniffer = new Sniffer(options);\n    sniffer.write(buffer);\n    return sniffer.encoding;\n}\n//# sourceMappingURL=sniffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\n");

/***/ })

};
;