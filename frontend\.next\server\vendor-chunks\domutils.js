"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(rsc)/./node_modules/domutils/lib/esm/feeds.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/feeds.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFeed: () => (/* binding */ getFeed)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n\n\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    const feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    const childs = feedRoot.children;\n    const feed = {\n        type: \"atom\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"entry\", childs).map((item) => {\n            var _a;\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            const href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            const description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            const pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    const href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    const updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    const childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    const feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"item\", feedRoot.children).map((item) => {\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            const pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    const updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nconst MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nconst MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"media:content\", where).map((elem) => {\n        const { attribs } = elem;\n        const media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (const attrib of MEDIA_KEYS_STRING) {\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (const attrib of MEDIA_KEYS_INT) {\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse = false) {\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent)((0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse = false) {\n    const val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/feeds.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/helpers.js":
/*!**************************************************!*\
  !*** ./node_modules/domutils/lib/esm/helpers.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* binding */ DocumentPosition),\n/* harmony export */   compareDocumentPosition: () => (/* binding */ compareDocumentPosition),\n/* harmony export */   removeSubsets: () => (/* binding */ removeSubsets),\n/* harmony export */   uniqueSort: () => (/* binding */ uniqueSort)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    let idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        const node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (let ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    const aParents = [];\n    const bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    let current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    const maxIdx = Math.min(aParents.length, bParents.length);\n    let idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    const sharedParent = aParents[idx - 1];\n    const siblings = sharedParent.children;\n    const aSibling = aParents[idx];\n    const bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter((node, i, arr) => !arr.includes(node, i + 1));\n    nodes.sort((a, b) => {\n        const relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.DocumentPosition),\n/* harmony export */   append: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.append),\n/* harmony export */   appendChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.appendChild),\n/* harmony export */   compareDocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.compareDocumentPosition),\n/* harmony export */   existsOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.existsOne),\n/* harmony export */   filter: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.filter),\n/* harmony export */   find: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.find),\n/* harmony export */   findAll: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findAll),\n/* harmony export */   findOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOne),\n/* harmony export */   findOneChild: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOneChild),\n/* harmony export */   getAttributeValue: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getAttributeValue),\n/* harmony export */   getChildren: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getChildren),\n/* harmony export */   getElementById: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementById),\n/* harmony export */   getElements: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElements),\n/* harmony export */   getElementsByClassName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagType),\n/* harmony export */   getFeed: () => (/* reexport safe */ _feeds_js__WEBPACK_IMPORTED_MODULE_6__.getFeed),\n/* harmony export */   getInnerHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getInnerHTML),\n/* harmony export */   getName: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getName),\n/* harmony export */   getOuterHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getOuterHTML),\n/* harmony export */   getParent: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getParent),\n/* harmony export */   getSiblings: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getSiblings),\n/* harmony export */   getText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getText),\n/* harmony export */   hasAttrib: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.hasAttrib),\n/* harmony export */   hasChildren: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.hasChildren),\n/* harmony export */   innerText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.innerText),\n/* harmony export */   isCDATA: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isComment),\n/* harmony export */   isDocument: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isText),\n/* harmony export */   nextElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.nextElementSibling),\n/* harmony export */   prepend: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prepend),\n/* harmony export */   prependChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prependChild),\n/* harmony export */   prevElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.prevElementSibling),\n/* harmony export */   removeElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.removeElement),\n/* harmony export */   removeSubsets: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.removeSubsets),\n/* harmony export */   replaceElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.replaceElement),\n/* harmony export */   testElement: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.testElement),\n/* harmony export */   textContent: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent),\n/* harmony export */   uniqueSort: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.uniqueSort)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _traversal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./traversal.js */ \"(rsc)/./node_modules/domutils/lib/esm/traversal.js\");\n/* harmony import */ var _manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./manipulation.js */ \"(rsc)/./node_modules/domutils/lib/esm/manipulation.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/./node_modules/domutils/lib/esm/helpers.js\");\n/* harmony import */ var _feeds_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./feeds.js */ \"(rsc)/./node_modules/domutils/lib/esm/feeds.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n\n\n\n\n\n/** @deprecated Use these methods from `domhandler` directly. */\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNBO0FBQ0c7QUFDSjtBQUNGO0FBQ0M7QUFDRjtBQUMzQjtBQUN5RjtBQUN6RiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEb2N1bWVudHNcXDNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGRvbXV0aWxzXFxsaWJcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vc3RyaW5naWZ5LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90cmF2ZXJzYWwuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL21hbmlwdWxhdGlvbi5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcXVlcnlpbmcuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2xlZ2FjeS5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vaGVscGVycy5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZmVlZHMuanNcIjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgdGhlc2UgbWV0aG9kcyBmcm9tIGBkb21oYW5kbGVyYCBkaXJlY3RseS4gKi9cbmV4cG9ydCB7IGlzVGFnLCBpc0NEQVRBLCBpc1RleHQsIGlzQ29tbWVudCwgaXNEb2N1bWVudCwgaGFzQ2hpbGRyZW4sIH0gZnJvbSBcImRvbWhhbmRsZXJcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/legacy.js":
/*!*************************************************!*\
  !*** ./node_modules/domutils/lib/esm/legacy.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   getElements: () => (/* binding */ getElements),\n/* harmony export */   getElementsByClassName: () => (/* binding */ getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* binding */ getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* binding */ getElementsByTagType),\n/* harmony export */   testElement: () => (/* binding */ testElement)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n\n\n/**\n * A map of functions to check nodes against.\n */\nconst Checks = {\n    tag_name(name) {\n        if (typeof name === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && name(elem.name);\n        }\n        else if (name === \"*\") {\n            return domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag;\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === name;\n    },\n    tag_type(type) {\n        if (typeof type === \"function\") {\n            return (elem) => type(elem.type);\n        }\n        return (elem) => elem.type === type;\n    },\n    tag_contains(data) {\n        if (typeof data === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && data(elem.data);\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && elem.data === data;\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && value(elem.attribs[attrib]);\n    }\n    return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.attribs[attrib] === value;\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return (elem) => a(elem) || b(elem);\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    const funcs = Object.keys(options).map((key) => {\n        const value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    const test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit = Infinity) {\n    const test = compileTest(options);\n    return test ? (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse = true) {\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL2VzbS9sZWdhY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMkM7QUFDSztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsaURBQUs7QUFDbEM7QUFDQTtBQUNBLG1CQUFtQiw2Q0FBSztBQUN4QjtBQUNBLHlCQUF5QixpREFBSztBQUM5QixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsNkJBQTZCLGtEQUFNO0FBQ25DO0FBQ0EseUJBQXlCLGtEQUFNO0FBQy9CLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsaURBQUs7QUFDOUI7QUFDQSxxQkFBcUIsaURBQUs7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxrQkFBa0Isb0RBQU07QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxXQUFXLHFEQUFPO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsb0RBQU07QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyxvREFBTTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxXQUFXLG9EQUFNO0FBQ2pCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRG9jdW1lbnRzXFwzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkb211dGlsc1xcbGliXFxlc21cXGxlZ2FjeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1RhZywgaXNUZXh0IH0gZnJvbSBcImRvbWhhbmRsZXJcIjtcbmltcG9ydCB7IGZpbHRlciwgZmluZE9uZSB9IGZyb20gXCIuL3F1ZXJ5aW5nLmpzXCI7XG4vKipcbiAqIEEgbWFwIG9mIGZ1bmN0aW9ucyB0byBjaGVjayBub2RlcyBhZ2FpbnN0LlxuICovXG5jb25zdCBDaGVja3MgPSB7XG4gICAgdGFnX25hbWUobmFtZSkge1xuICAgICAgICBpZiAodHlwZW9mIG5hbWUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgcmV0dXJuIChlbGVtKSA9PiBpc1RhZyhlbGVtKSAmJiBuYW1lKGVsZW0ubmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAobmFtZSA9PT0gXCIqXCIpIHtcbiAgICAgICAgICAgIHJldHVybiBpc1RhZztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gKGVsZW0pID0+IGlzVGFnKGVsZW0pICYmIGVsZW0ubmFtZSA9PT0gbmFtZTtcbiAgICB9LFxuICAgIHRhZ190eXBlKHR5cGUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0eXBlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgIHJldHVybiAoZWxlbSkgPT4gdHlwZShlbGVtLnR5cGUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoZWxlbSkgPT4gZWxlbS50eXBlID09PSB0eXBlO1xuICAgIH0sXG4gICAgdGFnX2NvbnRhaW5zKGRhdGEpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgIHJldHVybiAoZWxlbSkgPT4gaXNUZXh0KGVsZW0pICYmIGRhdGEoZWxlbS5kYXRhKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gKGVsZW0pID0+IGlzVGV4dChlbGVtKSAmJiBlbGVtLmRhdGEgPT09IGRhdGE7XG4gICAgfSxcbn07XG4vKipcbiAqIFJldHVybnMgYSBmdW5jdGlvbiB0byBjaGVjayB3aGV0aGVyIGEgbm9kZSBoYXMgYW4gYXR0cmlidXRlIHdpdGggYSBwYXJ0aWN1bGFyXG4gKiB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0gYXR0cmliIEF0dHJpYnV0ZSB0byBjaGVjay5cbiAqIEBwYXJhbSB2YWx1ZSBBdHRyaWJ1dGUgdmFsdWUgdG8gbG9vayBmb3IuXG4gKiBAcmV0dXJucyBBIGZ1bmN0aW9uIHRvIGNoZWNrIHdoZXRoZXIgdGhlIGEgbm9kZSBoYXMgYW4gYXR0cmlidXRlIHdpdGggYVxuICogICBwYXJ0aWN1bGFyIHZhbHVlLlxuICovXG5mdW5jdGlvbiBnZXRBdHRyaWJDaGVjayhhdHRyaWIsIHZhbHVlKSB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIHJldHVybiAoZWxlbSkgPT4gaXNUYWcoZWxlbSkgJiYgdmFsdWUoZWxlbS5hdHRyaWJzW2F0dHJpYl0pO1xuICAgIH1cbiAgICByZXR1cm4gKGVsZW0pID0+IGlzVGFnKGVsZW0pICYmIGVsZW0uYXR0cmlic1thdHRyaWJdID09PSB2YWx1ZTtcbn1cbi8qKlxuICogUmV0dXJucyBhIGZ1bmN0aW9uIHRoYXQgcmV0dXJucyBgdHJ1ZWAgaWYgZWl0aGVyIG9mIHRoZSBpbnB1dCBmdW5jdGlvbnNcbiAqIHJldHVybnMgYHRydWVgIGZvciBhIG5vZGUuXG4gKlxuICogQHBhcmFtIGEgRmlyc3QgZnVuY3Rpb24gdG8gY29tYmluZS5cbiAqIEBwYXJhbSBiIFNlY29uZCBmdW5jdGlvbiB0byBjb21iaW5lLlxuICogQHJldHVybnMgQSBmdW5jdGlvbiB0YWtpbmcgYSBub2RlIGFuZCByZXR1cm5pbmcgYHRydWVgIGlmIGVpdGhlciBvZiB0aGUgaW5wdXRcbiAqICAgZnVuY3Rpb25zIHJldHVybnMgYHRydWVgIGZvciB0aGUgbm9kZS5cbiAqL1xuZnVuY3Rpb24gY29tYmluZUZ1bmNzKGEsIGIpIHtcbiAgICByZXR1cm4gKGVsZW0pID0+IGEoZWxlbSkgfHwgYihlbGVtKTtcbn1cbi8qKlxuICogUmV0dXJucyBhIGZ1bmN0aW9uIHRoYXQgZXhlY3V0ZXMgYWxsIGNoZWNrcyBpbiBgb3B0aW9uc2AgYW5kIHJldHVybnMgYHRydWVgXG4gKiBpZiBhbnkgb2YgdGhlbSBtYXRjaCBhIG5vZGUuXG4gKlxuICogQHBhcmFtIG9wdGlvbnMgQW4gb2JqZWN0IGRlc2NyaWJpbmcgbm9kZXMgdG8gbG9vayBmb3IuXG4gKiBAcmV0dXJucyBBIGZ1bmN0aW9uIHRoYXQgZXhlY3V0ZXMgYWxsIGNoZWNrcyBpbiBgb3B0aW9uc2AgYW5kIHJldHVybnMgYHRydWVgXG4gKiAgIGlmIGFueSBvZiB0aGVtIG1hdGNoIGEgbm9kZS5cbiAqL1xuZnVuY3Rpb24gY29tcGlsZVRlc3Qob3B0aW9ucykge1xuICAgIGNvbnN0IGZ1bmNzID0gT2JqZWN0LmtleXMob3B0aW9ucykubWFwKChrZXkpID0+IHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBvcHRpb25zW2tleV07XG4gICAgICAgIHJldHVybiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoQ2hlY2tzLCBrZXkpXG4gICAgICAgICAgICA/IENoZWNrc1trZXldKHZhbHVlKVxuICAgICAgICAgICAgOiBnZXRBdHRyaWJDaGVjayhrZXksIHZhbHVlKTtcbiAgICB9KTtcbiAgICByZXR1cm4gZnVuY3MubGVuZ3RoID09PSAwID8gbnVsbCA6IGZ1bmNzLnJlZHVjZShjb21iaW5lRnVuY3MpO1xufVxuLyoqXG4gKiBDaGVja3Mgd2hldGhlciBhIG5vZGUgbWF0Y2hlcyB0aGUgZGVzY3JpcHRpb24gaW4gYG9wdGlvbnNgLlxuICpcbiAqIEBjYXRlZ29yeSBMZWdhY3kgUXVlcnkgRnVuY3Rpb25zXG4gKiBAcGFyYW0gb3B0aW9ucyBBbiBvYmplY3QgZGVzY3JpYmluZyBub2RlcyB0byBsb29rIGZvci5cbiAqIEBwYXJhbSBub2RlIFRoZSBlbGVtZW50IHRvIHRlc3QuXG4gKiBAcmV0dXJucyBXaGV0aGVyIHRoZSBlbGVtZW50IG1hdGNoZXMgdGhlIGRlc2NyaXB0aW9uIGluIGBvcHRpb25zYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRlc3RFbGVtZW50KG9wdGlvbnMsIG5vZGUpIHtcbiAgICBjb25zdCB0ZXN0ID0gY29tcGlsZVRlc3Qob3B0aW9ucyk7XG4gICAgcmV0dXJuIHRlc3QgPyB0ZXN0KG5vZGUpIDogdHJ1ZTtcbn1cbi8qKlxuICogUmV0dXJucyBhbGwgbm9kZXMgdGhhdCBtYXRjaCBgb3B0aW9uc2AuXG4gKlxuICogQGNhdGVnb3J5IExlZ2FjeSBRdWVyeSBGdW5jdGlvbnNcbiAqIEBwYXJhbSBvcHRpb25zIEFuIG9iamVjdCBkZXNjcmliaW5nIG5vZGVzIHRvIGxvb2sgZm9yLlxuICogQHBhcmFtIG5vZGVzIE5vZGVzIHRvIHNlYXJjaCB0aHJvdWdoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgdGhhdCBtYXRjaCBgb3B0aW9uc2AuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbGVtZW50cyhvcHRpb25zLCBub2RlcywgcmVjdXJzZSwgbGltaXQgPSBJbmZpbml0eSkge1xuICAgIGNvbnN0IHRlc3QgPSBjb21waWxlVGVzdChvcHRpb25zKTtcbiAgICByZXR1cm4gdGVzdCA/IGZpbHRlcih0ZXN0LCBub2RlcywgcmVjdXJzZSwgbGltaXQpIDogW107XG59XG4vKipcbiAqIFJldHVybnMgdGhlIG5vZGUgd2l0aCB0aGUgc3VwcGxpZWQgSUQuXG4gKlxuICogQGNhdGVnb3J5IExlZ2FjeSBRdWVyeSBGdW5jdGlvbnNcbiAqIEBwYXJhbSBpZCBUaGUgdW5pcXVlIElEIGF0dHJpYnV0ZSB2YWx1ZSB0byBsb29rIGZvci5cbiAqIEBwYXJhbSBub2RlcyBOb2RlcyB0byBzZWFyY2ggdGhyb3VnaC5cbiAqIEBwYXJhbSByZWN1cnNlIEFsc28gY29uc2lkZXIgY2hpbGQgbm9kZXMuXG4gKiBAcmV0dXJucyBUaGUgbm9kZSB3aXRoIHRoZSBzdXBwbGllZCBJRC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEVsZW1lbnRCeUlkKGlkLCBub2RlcywgcmVjdXJzZSA9IHRydWUpIHtcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkobm9kZXMpKVxuICAgICAgICBub2RlcyA9IFtub2Rlc107XG4gICAgcmV0dXJuIGZpbmRPbmUoZ2V0QXR0cmliQ2hlY2soXCJpZFwiLCBpZCksIG5vZGVzLCByZWN1cnNlKTtcbn1cbi8qKlxuICogUmV0dXJucyBhbGwgbm9kZXMgd2l0aCB0aGUgc3VwcGxpZWQgYHRhZ05hbWVgLlxuICpcbiAqIEBjYXRlZ29yeSBMZWdhY3kgUXVlcnkgRnVuY3Rpb25zXG4gKiBAcGFyYW0gdGFnTmFtZSBUYWcgbmFtZSB0byBzZWFyY2ggZm9yLlxuICogQHBhcmFtIG5vZGVzIE5vZGVzIHRvIHNlYXJjaCB0aHJvdWdoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgd2l0aCB0aGUgc3VwcGxpZWQgYHRhZ05hbWVgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RWxlbWVudHNCeVRhZ05hbWUodGFnTmFtZSwgbm9kZXMsIHJlY3Vyc2UgPSB0cnVlLCBsaW1pdCA9IEluZmluaXR5KSB7XG4gICAgcmV0dXJuIGZpbHRlcihDaGVja3NbXCJ0YWdfbmFtZVwiXSh0YWdOYW1lKSwgbm9kZXMsIHJlY3Vyc2UsIGxpbWl0KTtcbn1cbi8qKlxuICogUmV0dXJucyBhbGwgbm9kZXMgd2l0aCB0aGUgc3VwcGxpZWQgYGNsYXNzTmFtZWAuXG4gKlxuICogQGNhdGVnb3J5IExlZ2FjeSBRdWVyeSBGdW5jdGlvbnNcbiAqIEBwYXJhbSBjbGFzc05hbWUgQ2xhc3MgbmFtZSB0byBzZWFyY2ggZm9yLlxuICogQHBhcmFtIG5vZGVzIE5vZGVzIHRvIHNlYXJjaCB0aHJvdWdoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgd2l0aCB0aGUgc3VwcGxpZWQgYGNsYXNzTmFtZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbGVtZW50c0J5Q2xhc3NOYW1lKGNsYXNzTmFtZSwgbm9kZXMsIHJlY3Vyc2UgPSB0cnVlLCBsaW1pdCA9IEluZmluaXR5KSB7XG4gICAgcmV0dXJuIGZpbHRlcihnZXRBdHRyaWJDaGVjayhcImNsYXNzXCIsIGNsYXNzTmFtZSksIG5vZGVzLCByZWN1cnNlLCBsaW1pdCk7XG59XG4vKipcbiAqIFJldHVybnMgYWxsIG5vZGVzIHdpdGggdGhlIHN1cHBsaWVkIGB0eXBlYC5cbiAqXG4gKiBAY2F0ZWdvcnkgTGVnYWN5IFF1ZXJ5IEZ1bmN0aW9uc1xuICogQHBhcmFtIHR5cGUgRWxlbWVudCB0eXBlIHRvIGxvb2sgZm9yLlxuICogQHBhcmFtIG5vZGVzIE5vZGVzIHRvIHNlYXJjaCB0aHJvdWdoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgd2l0aCB0aGUgc3VwcGxpZWQgYHR5cGVgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RWxlbWVudHNCeVRhZ1R5cGUodHlwZSwgbm9kZXMsIHJlY3Vyc2UgPSB0cnVlLCBsaW1pdCA9IEluZmluaXR5KSB7XG4gICAgcmV0dXJuIGZpbHRlcihDaGVja3NbXCJ0YWdfdHlwZVwiXSh0eXBlKSwgbm9kZXMsIHJlY3Vyc2UsIGxpbWl0KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWxlZ2FjeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/manipulation.js":
/*!*******************************************************!*\
  !*** ./node_modules/domutils/lib/esm/manipulation.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependChild: () => (/* binding */ prependChild),\n/* harmony export */   removeElement: () => (/* binding */ removeElement),\n/* harmony export */   replaceElement: () => (/* binding */ replaceElement)\n/* harmony export */ });\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        const childs = elem.parent.children;\n        const childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    const prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    const next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    const parent = (replacement.parent = elem.parent);\n    if (parent) {\n        const childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        const sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    const { parent } = elem;\n    const currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            const childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        const sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    const { parent } = elem;\n    if (parent) {\n        const childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/querying.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/esm/querying.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   existsOne: () => (/* binding */ existsOne),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findAll: () => (/* binding */ findAll),\n/* harmony export */   findOne: () => (/* binding */ findOne),\n/* harmony export */   findOneChild: () => (/* binding */ findOneChild)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse = true, limit = Infinity) {\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    const result = [];\n    /** Stack of the arrays we are looking at. */\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    const indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse = true) {\n    const searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (let i = 0; i < searchedNodes.length; i++) {\n        const node = searchedNodes[i];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && node.children.length > 0) {\n            const found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some((node) => ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) ||\n        ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && existsOne(test, node.children)));\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    const result = [];\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    const indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/querying.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/stringify.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/stringify.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInnerHTML: () => (/* binding */ getInnerHTML),\n/* harmony export */   getOuterHTML: () => (/* binding */ getOuterHTML),\n/* harmony export */   getText: () => (/* binding */ getText),\n/* harmony export */   innerText: () => (/* binding */ innerText),\n/* harmony export */   textContent: () => (/* binding */ textContent)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n\n\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0,dom_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node)\n        ? node.children.map((node) => getOuterHTML(node, options)).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))\n        return getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && (node.type === domelementtype__WEBPACK_IMPORTED_MODULE_2__.ElementType.Tag || (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/traversal.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/traversal.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributeValue: () => (/* binding */ getAttributeValue),\n/* harmony export */   getChildren: () => (/* binding */ getChildren),\n/* harmony export */   getName: () => (/* binding */ getName),\n/* harmony export */   getParent: () => (/* binding */ getParent),\n/* harmony export */   getSiblings: () => (/* binding */ getSiblings),\n/* harmony export */   hasAttrib: () => (/* binding */ hasAttrib),\n/* harmony export */   nextElementSibling: () => (/* binding */ nextElementSibling),\n/* harmony export */   prevElementSibling: () => (/* binding */ prevElementSibling)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    const parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    const siblings = [elem];\n    let { prev, next } = elem;\n    while (prev != null) {\n        siblings.unshift(prev);\n        ({ prev } = prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        ({ next } = next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    let { next } = elem;\n    while (next !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(next))\n        ({ next } = next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    let { prev } = elem;\n    while (prev !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(prev))\n        ({ prev } = prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/traversal.js\n");

/***/ })

};
;