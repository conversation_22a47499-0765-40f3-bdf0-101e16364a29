{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/fiber": "^9.2.0", "@supabase/supabase-js": "^2.52.1", "@types/cheerio": "^0.22.35", "@types/three": "^0.178.1", "autoprefixer": "^10.4.21", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.9", "lucide-react": "^0.525.0", "next": "15.4.4", "radix-ui": "^1.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-mui-sidebar": "^1.6.3", "tailwind-merge": "^3.3.1", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}