'use client'

import PositionsTable from "@/components/positions-table"
import Calculator from "@/components/calculator"
import { useFootballEvents } from "@/hooks/useLiveResults"
import { useMultipleMatchDetails } from "@/hooks/useMatchDetails"
import PositionsHeader from "@/components/positions-header"

export default function Dashboard() {
  // Buscar eventos de futebol ao vivo
  const { events, loading, error, refetch, lastUpdated } = useFootballEvents()

  // Pegar os primeiros 4 eventos para o dashboard
  const topEvents = events.slice(0, 4)
  const eventIds = topEvents.map(event => event.event_id)

  // Buscar detalhes para estes eventos
  const { results: matchDetails, loading: detailsLoading } = useMultipleMatchDetails(eventIds)

  // Combinar dados dos eventos com seus detalhes
  const eventsWithDetails = topEvents.map(event => ({
    ...event,
    homeTeam: matchDetails[event.event_id]?.homeTeam || event.homeTeam,
    awayTeam: matchDetails[event.event_id]?.awayTeam || event.awayTeam,
    homeTeamLogo: matchDetails[event.event_id]?.homeTeamLogo,
    awayTeamLogo: matchDetails[event.event_id]?.awayTeamLogo,
    competition: matchDetails[event.event_id]?.tournament || event.competition,
    country: matchDetails[event.event_id]?.country || event.country
  }))

  const isLoading = loading || detailsLoading
  return (
    <div className="h-full grid grid-cols-[2fr_1fr] gap-4">
      {/* Coluna esquerda - Múltiplos Jogos */}
      <div className="flex flex-col h-full gap-1 overflow-y-auto">
        {/* Header único para todas as posições */}
        <PositionsHeader />
        {/* Jogo 1 - Dados reais ou fallback */}
        <div className="bg-card">
          <PositionsTable
            matchData={eventsWithDetails[0]}
            loading={isLoading}
            error={error}
            onRefresh={refetch}
            // Fallback para dados estáticos se não houver dados reais
            gameTitle={eventsWithDetails[0] ? undefined : "Real Madrid x Inter de Milão"}
            league={eventsWithDetails[0] ? undefined : "Champions League"}
            time={eventsWithDetails[0] ? undefined : "Hoje, 20:00"}
          />
        </div>

        {/* Jogo 2 - Dados reais ou fallback */}
        <div className="bg-card ">
          <PositionsTable
            matchData={eventsWithDetails[1]}
            loading={isLoading}
            error={error}
            onRefresh={refetch}
            // Fallback para dados estáticos se não houver dados reais
            gameTitle={eventsWithDetails[1] ? undefined : "Barcelona x PSG"}
            league={eventsWithDetails[1] ? undefined : "Champions League"}
            time={eventsWithDetails[1] ? undefined : "Amanhã, 16:45"}
          />
        </div>

        {/* Jogo 3 - Dados reais ou fallback */}
        <div className="bg-card ">
          <PositionsTable
            matchData={eventsWithDetails[2]}
            loading={isLoading}
            error={error}
            onRefresh={refetch}
            // Fallback para dados estáticos se não houver dados reais
            gameTitle={eventsWithDetails[2] ? undefined : "Manchester City x Arsenal"}
            league={eventsWithDetails[2] ? undefined : "Premier League"}
            time={eventsWithDetails[2] ? undefined : "Sábado, 14:30"}
          />
        </div>

        {/* Jogo 4 - Dados reais ou fallback */}
        <div className="bg-card ">
          <PositionsTable
            matchData={eventsWithDetails[3]}
            loading={isLoading}
            error={error}
            onRefresh={refetch}
            // Fallback para dados estáticos se não houver dados reais
            gameTitle={eventsWithDetails[3] ? undefined : "Bayern Munich x Borussia Dortmund"}
            league={eventsWithDetails[3] ? undefined : "Bundesliga"}
            time={eventsWithDetails[3] ? undefined : "Domingo, 17:00"}
          />
        </div>
      </div>

      {/* Coluna direita - Calculadora */}
      <div>
        <Calculator />
      </div>
    </div>
  );
}
