'use client';

import React, { useState, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useSidebar } from "@/contexts/SidebarContext";
import {
  Tooltip,
  TooltipContent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Ícones básicos (você pode adicionar mais ícones conforme necessário)
const HomeIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
  </svg>
);

const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
  </svg>
);

const AnalyticsIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
  </svg>
);

const ChatIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
  </svg>
);

const CalendarIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
  </svg>
);



const AppSidebar = () => {
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const { isCollapsed, toggleSidebar } = useSidebar();
  const pathname = usePathname();

  const toggleSubmenu = useCallback((menu: string) => {
    setOpenSubmenu(prev => prev === menu ? null : menu);
  }, []);

  // Função para verificar se o link está ativo
  const isLinkActive = (href: string) => {
    if (href === '/') {
      return pathname === '/' || pathname === '/login';
    }
    return pathname.startsWith(href);
  };

  // Função para obter as classes do link baseado no estado ativo
  const getLinkClasses = (href: string, baseClasses: string) => {
    const isActive = isLinkActive(href);
    if (isActive) {
      return baseClasses.replace('text-muted-foreground hover:text-foreground hover:bg-accent', 'bg-primary text-primary-foreground');
    }
    return baseClasses;
  };

  return (
    <TooltipProvider delayDuration={0}>
      <div className={`${isCollapsed ? 'w-16' : 'w-[270px]'} h-full bg-card border-r border-border flex flex-col transition-all duration-300 ease-in-out`}>
      {/* Botão de Expandir/Colapsar */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          {isCollapsed ? (
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={toggleSidebar}
                  className="flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 4l8 8-8 8"
                    />
                  </svg>
                </button>
              </TooltipTrigger>
              <TooltipContent side="right" className="px-2 py-1 text-xs">
                Expandir sidebar
              </TooltipContent>
            </Tooltip>
          ) : (
            <button
              onClick={toggleSidebar}
              className="flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 4l-8 8 8 8"
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Logo */}
      <div className="p-4 border-b border-border">
        <Link href="/dashboard" className="flex items-center gap-3">
          <Image
            src={isCollapsed ? "/icon-logo.svg" : "/logo.svg"}
            alt="Profit Growth"
            width={32}
            height={32}
            className="h-8 w-auto"
          />
          
        </Link>
      </div>

      {/* Menu */}
      <div className="flex-1 overflow-y-auto">
        {/* PRINCIPAL */}
        <div className="p-4">
          {!isCollapsed && (
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
              PRINCIPAL
            </h3>
          )}
          <nav className="space-y-1">
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/"
                    className={getLinkClasses("/", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <HomeIcon />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Início
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/"
                className={getLinkClasses("/", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <HomeIcon />
                Início
                {isLinkActive("/") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard"
                    className={getLinkClasses("/dashboard", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <DashboardIcon />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Dashboard
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/dashboard"
                className={getLinkClasses("/dashboard", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <DashboardIcon />
                Dashboard
                {isLinkActive("/dashboard") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/analytics"
                    className={getLinkClasses("/analytics", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <AnalyticsIcon />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Analytics
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/analytics"
                className={getLinkClasses("/analytics", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <AnalyticsIcon />
                Analytics
                {isLinkActive("/analytics") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
          </nav>
        </div>

        {/* APLICAÇÕES */}
        <div className="p-4">
          {!isCollapsed && (
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
              APLICAÇÕES
            </h3>
          )}
          <nav className="space-y-1">
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/chat"
                    className={getLinkClasses("/chat", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <ChatIcon />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Chat
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/chat"
                className={getLinkClasses("/chat", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <ChatIcon />
                Chat
                {isLinkActive("/chat") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/calendar"
                    className={getLinkClasses("/calendar", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <CalendarIcon />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Calendário
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/calendar"
                className={getLinkClasses("/calendar", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <CalendarIcon />
                Calendário
                {isLinkActive("/calendar") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
          </nav>
        </div>

        {/* OUTROS */}
        <div className="p-4">
          {!isCollapsed && (
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
              OUTROS
            </h3>
          )}
          <nav className="space-y-1">
            {!isCollapsed ? (
              <div>
                <button
                  onClick={() => toggleSubmenu('multilevel')}
                  className="flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full text-left"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                  Menu Multinível
                  <svg
                    className={`w-4 h-4 ml-auto transition-transform ${
                      openSubmenu === 'multilevel' ? 'rotate-90' : ''
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                {openSubmenu === 'multilevel' && (
                  <div className="ml-6 mt-1 space-y-1">
                    <Link
                      href="/posts"
                      className={getLinkClasses("/posts", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                    >
                      Posts
                      {isLinkActive("/posts") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
                    </Link>
                    <Link
                      href="/details"
                      className={getLinkClasses("/details", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                    >
                      Detalhes
                      {isLinkActive("/details") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button className="flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Menu Multinível
                </TooltipContent>
              </Tooltip>
            )}
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/sobre"
                    className={getLinkClasses("/sobre", "flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Sobre
                </TooltipContent>
              </Tooltip>
            ) : (
              <Link
                href="/sobre"
                className={getLinkClasses("/sobre", "flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent")}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Sobre
                {isLinkActive("/sobre") && <span className="ml-auto w-2 h-2 bg-primary-foreground rounded-full"></span>}
              </Link>
            )}
            {isCollapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <a
                    href="https://github.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </a>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  Link Externo
                </TooltipContent>
              </Tooltip>
            ) : (
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Link Externo
                <svg className="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </a>
            )}
          </nav>
        </div>
      </div>
    </div>
    </TooltipProvider>
  );
};

export default AppSidebar;
