"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cheerio";
exports.ids = ["vendor-chunks/cheerio"];
exports.modules = {

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js":
/*!*********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/attributes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   attr: () => (/* binding */ attr),\n/* harmony export */   data: () => (/* binding */ data),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   prop: () => (/* binding */ prop),\n/* harmony export */   removeAttr: () => (/* binding */ removeAttr),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   toggleClass: () => (/* binding */ toggleClass),\n/* harmony export */   val: () => (/* binding */ val)\n/* harmony export */ });\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/**\n * Methods for getting and modifying attributes.\n *\n * @module cheerio/attributes\n */\nvar _a;\n\n\n\n\n\nconst hasOwn = \n// @ts-expect-error `hasOwn` is a standard object method\n(_a = Object.hasOwn) !== null && _a !== void 0 ? _a : ((object, prop) => Object.prototype.hasOwnProperty.call(object, prop));\nconst rspace = /\\s+/;\nconst dataAttrPrefix = 'data-';\n// Attributes that are booleans\nconst rboolean = /^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i;\n// Matches strings that look like JSON objects or arrays\nconst rbrace = /^{[^]*}$|^\\[[^]*]$/;\nfunction getAttr(elem, name, xmlMode) {\n    var _a;\n    if (!elem || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n        return undefined;\n    (_a = elem.attribs) !== null && _a !== void 0 ? _a : (elem.attribs = {});\n    // Return the entire attribs object if no attribute specified\n    if (!name) {\n        return elem.attribs;\n    }\n    if (hasOwn(elem.attribs, name)) {\n        // Get the (decoded) attribute\n        return !xmlMode && rboolean.test(name) ? name : elem.attribs[name];\n    }\n    // Mimic the DOM and return text content as value for `option's`\n    if (elem.name === 'option' && name === 'value') {\n        return (0,_static_js__WEBPACK_IMPORTED_MODULE_0__.text)(elem.children);\n    }\n    // Mimic DOM with default value for radios/checkboxes\n    if (elem.name === 'input' &&\n        (elem.attribs['type'] === 'radio' || elem.attribs['type'] === 'checkbox') &&\n        name === 'value') {\n        return 'on';\n    }\n    return undefined;\n}\n/**\n * Sets the value of an attribute. The attribute will be deleted if the value is\n * `null`.\n *\n * @private\n * @param el - The element to set the attribute on.\n * @param name - The attribute's name.\n * @param value - The attribute's value.\n */\nfunction setAttr(el, name, value) {\n    if (value === null) {\n        removeAttribute(el, name);\n    }\n    else {\n        el.attribs[name] = `${value}`;\n    }\n}\nfunction attr(name, value) {\n    // Set the value (with attr map support)\n    if (typeof name === 'object' || value !== undefined) {\n        if (typeof value === 'function') {\n            if (typeof name !== 'string') {\n                {\n                    throw new Error('Bad combination of arguments.');\n                }\n            }\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    setAttr(el, name, value.call(el, i, el.attribs[name]));\n            });\n        }\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                return;\n            if (typeof name === 'object') {\n                for (const objName of Object.keys(name)) {\n                    const objValue = name[objName];\n                    setAttr(el, objName, objValue);\n                }\n            }\n            else {\n                setAttr(el, name, value);\n            }\n        });\n    }\n    return arguments.length > 1\n        ? this\n        : getAttr(this[0], name, this.options.xmlMode);\n}\n/**\n * Gets a node's prop.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the prop of.\n * @param name - Name of the prop.\n * @param xmlMode - Disable handling of special HTML attributes.\n * @returns The prop's value.\n */\nfunction getProp(el, name, xmlMode) {\n    return name in el\n        ? // @ts-expect-error TS doesn't like us accessing the value directly here.\n            el[name]\n        : !xmlMode && rboolean.test(name)\n            ? getAttr(el, name, false) !== undefined\n            : getAttr(el, name, xmlMode);\n}\n/**\n * Sets the value of a prop.\n *\n * @private\n * @param el - The element to set the prop on.\n * @param name - The prop's name.\n * @param value - The prop's value.\n * @param xmlMode - Disable handling of special HTML attributes.\n */\nfunction setProp(el, name, value, xmlMode) {\n    if (name in el) {\n        // @ts-expect-error Overriding value\n        el[name] = value;\n    }\n    else {\n        setAttr(el, name, !xmlMode && rboolean.test(name)\n            ? value\n                ? ''\n                : null\n            : `${value}`);\n    }\n}\nfunction prop(name, value) {\n    var _a;\n    if (typeof name === 'string' && value === undefined) {\n        const el = this[0];\n        if (!el)\n            return undefined;\n        switch (name) {\n            case 'style': {\n                const property = this.css();\n                const keys = Object.keys(property);\n                for (let i = 0; i < keys.length; i++) {\n                    property[i] = keys[i];\n                }\n                property.length = keys.length;\n                return property;\n            }\n            case 'tagName':\n            case 'nodeName': {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                return el.name.toUpperCase();\n            }\n            case 'href':\n            case 'src': {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                const prop = (_a = el.attribs) === null || _a === void 0 ? void 0 : _a[name];\n                if (typeof URL !== 'undefined' &&\n                    ((name === 'href' && (el.tagName === 'a' || el.tagName === 'link')) ||\n                        (name === 'src' &&\n                            (el.tagName === 'img' ||\n                                el.tagName === 'iframe' ||\n                                el.tagName === 'audio' ||\n                                el.tagName === 'video' ||\n                                el.tagName === 'source'))) &&\n                    prop !== undefined &&\n                    this.options.baseURI) {\n                    return new URL(prop, this.options.baseURI).href;\n                }\n                return prop;\n            }\n            case 'innerText': {\n                return (0,domutils__WEBPACK_IMPORTED_MODULE_3__.innerText)(el);\n            }\n            case 'textContent': {\n                return (0,domutils__WEBPACK_IMPORTED_MODULE_3__.textContent)(el);\n            }\n            case 'outerHTML': {\n                if (el.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Root)\n                    return this.html();\n                return this.clone().wrap('<container />').parent().html();\n            }\n            case 'innerHTML': {\n                return this.html();\n            }\n            default: {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                return getProp(el, name, this.options.xmlMode);\n            }\n        }\n    }\n    if (typeof name === 'object' || value !== undefined) {\n        if (typeof value === 'function') {\n            if (typeof name === 'object') {\n                throw new TypeError('Bad combination of arguments.');\n            }\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                    setProp(el, name, value.call(el, i, getProp(el, name, this.options.xmlMode)), this.options.xmlMode);\n                }\n            });\n        }\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                return;\n            if (typeof name === 'object') {\n                for (const key of Object.keys(name)) {\n                    const val = name[key];\n                    setProp(el, key, val, this.options.xmlMode);\n                }\n            }\n            else {\n                setProp(el, name, value, this.options.xmlMode);\n            }\n        });\n    }\n    return undefined;\n}\n/**\n * Sets the value of a data attribute.\n *\n * @private\n * @param elem - The element to set the data attribute on.\n * @param name - The data attribute's name.\n * @param value - The data attribute's value.\n */\nfunction setData(elem, name, value) {\n    var _a;\n    (_a = elem.data) !== null && _a !== void 0 ? _a : (elem.data = {});\n    if (typeof name === 'object')\n        Object.assign(elem.data, name);\n    else if (typeof name === 'string' && value !== undefined) {\n        elem.data[name] = value;\n    }\n}\n/**\n * Read _all_ HTML5 `data-*` attributes from the equivalent HTML5 `data-*`\n * attribute, and cache the value in the node's internal data store.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the data attribute of.\n * @returns A map with all of the data attributes.\n */\nfunction readAllData(el) {\n    for (const domName of Object.keys(el.attribs)) {\n        if (!domName.startsWith(dataAttrPrefix)) {\n            continue;\n        }\n        const jsName = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.camelCase)(domName.slice(dataAttrPrefix.length));\n        if (!hasOwn(el.data, jsName)) {\n            el.data[jsName] = parseDataValue(el.attribs[domName]);\n        }\n    }\n    return el.data;\n}\n/**\n * Read the specified attribute from the equivalent HTML5 `data-*` attribute,\n * and (if present) cache the value in the node's internal data store.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the data attribute of.\n * @param name - Name of the data attribute.\n * @returns The data attribute's value.\n */\nfunction readData(el, name) {\n    const domName = dataAttrPrefix + (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.cssCase)(name);\n    const data = el.data;\n    if (hasOwn(data, name)) {\n        return data[name];\n    }\n    if (hasOwn(el.attribs, domName)) {\n        return (data[name] = parseDataValue(el.attribs[domName]));\n    }\n    return undefined;\n}\n/**\n * Coerce string data-* attributes to their corresponding JavaScript primitives.\n *\n * @private\n * @category Attributes\n * @param value - The value to parse.\n * @returns The parsed value.\n */\nfunction parseDataValue(value) {\n    if (value === 'null')\n        return null;\n    if (value === 'true')\n        return true;\n    if (value === 'false')\n        return false;\n    const num = Number(value);\n    if (value === String(num))\n        return num;\n    if (rbrace.test(value)) {\n        try {\n            return JSON.parse(value);\n        }\n        catch {\n            /* Ignore */\n        }\n    }\n    return value;\n}\nfunction data(name, value) {\n    var _a;\n    const elem = this[0];\n    if (!elem || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n        return;\n    const dataEl = elem;\n    (_a = dataEl.data) !== null && _a !== void 0 ? _a : (dataEl.data = {});\n    // Return the entire data object if no data specified\n    if (name == null) {\n        return readAllData(dataEl);\n    }\n    // Set the value (with attr map support)\n    if (typeof name === 'object' || value !== undefined) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                if (typeof name === 'object')\n                    setData(el, name);\n                else\n                    setData(el, name, value);\n            }\n        });\n        return this;\n    }\n    return readData(dataEl, name);\n}\nfunction val(value) {\n    const querying = arguments.length === 0;\n    const element = this[0];\n    if (!element || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(element))\n        return querying ? undefined : this;\n    switch (element.name) {\n        case 'textarea': {\n            return this.text(value);\n        }\n        case 'select': {\n            const option = this.find('option:selected');\n            if (!querying) {\n                if (this.attr('multiple') == null && typeof value === 'object') {\n                    return this;\n                }\n                this.find('option').removeAttr('selected');\n                const values = typeof value === 'object' ? value : [value];\n                for (const val of values) {\n                    this.find(`option[value=\"${val}\"]`).attr('selected', '');\n                }\n                return this;\n            }\n            return this.attr('multiple')\n                ? option.toArray().map((el) => (0,_static_js__WEBPACK_IMPORTED_MODULE_0__.text)(el.children))\n                : option.attr('value');\n        }\n        case 'input':\n        case 'option': {\n            return querying\n                ? this.attr('value')\n                : this.attr('value', value);\n        }\n    }\n    return undefined;\n}\n/**\n * Remove an attribute.\n *\n * @private\n * @param elem - Node to remove attribute from.\n * @param name - Name of the attribute to remove.\n */\nfunction removeAttribute(elem, name) {\n    if (!elem.attribs || !hasOwn(elem.attribs, name))\n        return;\n    delete elem.attribs[name];\n}\n/**\n * Splits a space-separated list of names to individual names.\n *\n * @category Attributes\n * @param names - Names to split.\n * @returns - Split names.\n */\nfunction splitNames(names) {\n    return names ? names.trim().split(rspace) : [];\n}\n/**\n * Method for removing attributes by `name`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').removeAttr('class').prop('outerHTML');\n * //=> <li>Pear</li>\n *\n * $('.apple').attr('id', 'favorite');\n * $('.apple').removeAttr('id class').prop('outerHTML');\n * //=> <li>Apple</li>\n * ```\n *\n * @param name - Name of the attribute.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/removeAttr/}\n */\nfunction removeAttr(name) {\n    const attrNames = splitNames(name);\n    for (const attrName of attrNames) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (elem) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n                removeAttribute(elem, attrName);\n        });\n    }\n    return this;\n}\n/**\n * Check to see if _any_ of the matched elements have the given `className`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').hasClass('pear');\n * //=> true\n *\n * $('apple').hasClass('fruit');\n * //=> false\n *\n * $('li').hasClass('pear');\n * //=> true\n * ```\n *\n * @param className - Name of the class.\n * @returns Indicates if an element has the given `className`.\n * @see {@link https://api.jquery.com/hasClass/}\n */\nfunction hasClass(className) {\n    return this.toArray().some((elem) => {\n        const clazz = (0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem) && elem.attribs['class'];\n        let idx = -1;\n        if (clazz && className.length > 0) {\n            while ((idx = clazz.indexOf(className, idx + 1)) > -1) {\n                const end = idx + className.length;\n                if ((idx === 0 || rspace.test(clazz[idx - 1])) &&\n                    (end === clazz.length || rspace.test(clazz[end]))) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    });\n}\n/**\n * Adds class(es) to all of the matched elements. Also accepts a `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').addClass('fruit').prop('outerHTML');\n * //=> <li class=\"pear fruit\">Pear</li>\n *\n * $('.apple').addClass('fruit red').prop('outerHTML');\n * //=> <li class=\"apple fruit red\">Apple</li>\n * ```\n *\n * @param value - Name of new class.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/addClass/}\n */\nfunction addClass(value) {\n    // Support functions\n    if (typeof value === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                const className = el.attribs['class'] || '';\n                addClass.call([el], value.call(el, i, className));\n            }\n        });\n    }\n    // Return if no value or not a string or function\n    if (!value || typeof value !== 'string')\n        return this;\n    const classNames = value.split(rspace);\n    const numElements = this.length;\n    for (let i = 0; i < numElements; i++) {\n        const el = this[i];\n        // If selected element isn't a tag, move on\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            continue;\n        // If we don't already have classes — always set xmlMode to false here, as it doesn't matter for classes\n        const className = getAttr(el, 'class', false);\n        if (className) {\n            let setClass = ` ${className} `;\n            // Check if class already exists\n            for (const cn of classNames) {\n                const appendClass = `${cn} `;\n                if (!setClass.includes(` ${appendClass}`))\n                    setClass += appendClass;\n            }\n            setAttr(el, 'class', setClass.trim());\n        }\n        else {\n            setAttr(el, 'class', classNames.join(' ').trim());\n        }\n    }\n    return this;\n}\n/**\n * Removes one or more space-separated classes from the selected elements. If no\n * `className` is defined, all classes will be removed. Also accepts a\n * `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').removeClass('pear').prop('outerHTML');\n * //=> <li class=\"\">Pear</li>\n *\n * $('.apple').addClass('red').removeClass().prop('outerHTML');\n * //=> <li class=\"\">Apple</li>\n * ```\n *\n * @param name - Name of the class. If not specified, removes all elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/removeClass/}\n */\nfunction removeClass(name) {\n    // Handle if value is a function\n    if (typeof name === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                removeClass.call([el], name.call(el, i, el.attribs['class'] || ''));\n            }\n        });\n    }\n    const classes = splitNames(name);\n    const numClasses = classes.length;\n    const removeAll = arguments.length === 0;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            return;\n        if (removeAll) {\n            // Short circuit the remove all case as this is the nice one\n            el.attribs['class'] = '';\n        }\n        else {\n            const elClasses = splitNames(el.attribs['class']);\n            let changed = false;\n            for (let j = 0; j < numClasses; j++) {\n                const index = elClasses.indexOf(classes[j]);\n                if (index !== -1) {\n                    elClasses.splice(index, 1);\n                    changed = true;\n                    /*\n                     * We have to do another pass to ensure that there are not duplicate\n                     * classes listed\n                     */\n                    j--;\n                }\n            }\n            if (changed) {\n                el.attribs['class'] = elClasses.join(' ');\n            }\n        }\n    });\n}\n/**\n * Add or remove class(es) from the matched elements, depending on either the\n * class's presence or the value of the switch argument. Also accepts a\n * `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.apple.green').toggleClass('fruit green red').prop('outerHTML');\n * //=> <li class=\"apple fruit red\">Apple</li>\n *\n * $('.apple.green').toggleClass('fruit green red', true).prop('outerHTML');\n * //=> <li class=\"apple green fruit red\">Apple</li>\n * ```\n *\n * @param value - Name of the class. Can also be a function.\n * @param stateVal - If specified the state of the class.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/toggleClass/}\n */\nfunction toggleClass(value, stateVal) {\n    // Support functions\n    if (typeof value === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                toggleClass.call([el], value.call(el, i, el.attribs['class'] || '', stateVal), stateVal);\n            }\n        });\n    }\n    // Return if no value or not a string or function\n    if (!value || typeof value !== 'string')\n        return this;\n    const classNames = value.split(rspace);\n    const numClasses = classNames.length;\n    const state = typeof stateVal === 'boolean' ? (stateVal ? 1 : -1) : 0;\n    const numElements = this.length;\n    for (let i = 0; i < numElements; i++) {\n        const el = this[i];\n        // If selected element isn't a tag, move on\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            continue;\n        const elementClasses = splitNames(el.attribs['class']);\n        // Check if class already exists\n        for (let j = 0; j < numClasses; j++) {\n            // Check if the class name is currently defined\n            const index = elementClasses.indexOf(classNames[j]);\n            // Add if stateValue === true or we are toggling and there is no value\n            if (state >= 0 && index === -1) {\n                elementClasses.push(classNames[j]);\n            }\n            else if (state <= 0 && index !== -1) {\n                // Otherwise remove but only if the item exists\n                elementClasses.splice(index, 1);\n            }\n        }\n        el.attribs['class'] = elementClasses.join(' ');\n    }\n    return this;\n}\n//# sourceMappingURL=attributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/css.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/css.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n/**\n * Set multiple CSS properties for every matched element.\n *\n * @category CSS\n * @param prop - The names of the properties.\n * @param val - The new values.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/css/}\n */\nfunction css(prop, val) {\n    if ((prop != null && val != null) ||\n        // When `prop` is a \"plain\" object\n        (typeof prop === 'object' && !Array.isArray(prop))) {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag)(el)) {\n                // `prop` can't be an array here anymore.\n                setCss(el, prop, val, i);\n            }\n        });\n    }\n    if (this.length === 0) {\n        return undefined;\n    }\n    return getCss(this[0], prop);\n}\n/**\n * Set styles of all elements.\n *\n * @private\n * @param el - Element to set style of.\n * @param prop - Name of property.\n * @param value - Value to set property to.\n * @param idx - Optional index within the selection.\n */\nfunction setCss(el, prop, value, idx) {\n    if (typeof prop === 'string') {\n        const styles = getCss(el);\n        const val = typeof value === 'function' ? value.call(el, idx, styles[prop]) : value;\n        if (val === '') {\n            delete styles[prop];\n        }\n        else if (val != null) {\n            styles[prop] = val;\n        }\n        el.attribs['style'] = stringify(styles);\n    }\n    else if (typeof prop === 'object') {\n        const keys = Object.keys(prop);\n        for (let i = 0; i < keys.length; i++) {\n            const k = keys[i];\n            setCss(el, k, prop[k], i);\n        }\n    }\n}\nfunction getCss(el, prop) {\n    if (!el || !(0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag)(el))\n        return;\n    const styles = parse(el.attribs['style']);\n    if (typeof prop === 'string') {\n        return styles[prop];\n    }\n    if (Array.isArray(prop)) {\n        const newStyles = {};\n        for (const item of prop) {\n            if (styles[item] != null) {\n                newStyles[item] = styles[item];\n            }\n        }\n        return newStyles;\n    }\n    return styles;\n}\n/**\n * Stringify `obj` to styles.\n *\n * @private\n * @category CSS\n * @param obj - Object to stringify.\n * @returns The serialized styles.\n */\nfunction stringify(obj) {\n    return Object.keys(obj).reduce((str, prop) => `${str}${str ? ' ' : ''}${prop}: ${obj[prop]};`, '');\n}\n/**\n * Parse `styles`.\n *\n * @private\n * @category CSS\n * @param styles - Styles to be parsed.\n * @returns The parsed styles.\n */\nfunction parse(styles) {\n    styles = (styles || '').trim();\n    if (!styles)\n        return {};\n    const obj = {};\n    let key;\n    for (const str of styles.split(';')) {\n        const n = str.indexOf(':');\n        // If there is no :, or if it is the first/last character, add to the previous item's value\n        if (n < 1 || n === str.length - 1) {\n            const trimmed = str.trimEnd();\n            if (trimmed.length > 0 && key !== undefined) {\n                obj[key] += `;${trimmed}`;\n            }\n        }\n        else {\n            key = str.slice(0, n).trim();\n            obj[key] = str.slice(n + 1).trim();\n        }\n    }\n    return obj;\n}\n//# sourceMappingURL=css.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/css.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/extract.js":
/*!******************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/extract.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extract: () => (/* binding */ extract)\n/* harmony export */ });\nfunction getExtractDescr(descr) {\n    var _a;\n    if (typeof descr === 'string') {\n        return { selector: descr, value: 'textContent' };\n    }\n    return {\n        selector: descr.selector,\n        value: (_a = descr.value) !== null && _a !== void 0 ? _a : 'textContent',\n    };\n}\n/**\n * Extract multiple values from a document, and store them in an object.\n *\n * @param map - An object containing key-value pairs. The keys are the names of\n *   the properties to be created on the object, and the values are the\n *   selectors to be used to extract the values.\n * @returns An object containing the extracted values.\n */\nfunction extract(map) {\n    const ret = {};\n    for (const key in map) {\n        const descr = map[key];\n        const isArray = Array.isArray(descr);\n        const { selector, value } = getExtractDescr(isArray ? descr[0] : descr);\n        const fn = typeof value === 'function'\n            ? value\n            : typeof value === 'string'\n                ? (el) => this._make(el).prop(value)\n                : (el) => this._make(el).extract(value);\n        if (isArray) {\n            ret[key] = this._findBySelector(selector, Number.POSITIVE_INFINITY)\n                .map((_, el) => fn(el, key, ret))\n                .get();\n        }\n        else {\n            const $ = this._findBySelector(selector, 1);\n            ret[key] = $.length > 0 ? fn($[0], key, ret) : undefined;\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=extract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/extract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/forms.js":
/*!****************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/forms.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeArray: () => (/* binding */ serializeArray)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/*\n * https://github.com/jquery/jquery/blob/2.1.3/src/manipulation/var/rcheckableType.js\n * https://github.com/jquery/jquery/blob/2.1.3/src/serialize.js\n */\nconst submittableSelector = 'input,select,textarea,keygen';\nconst r20 = /%20/g;\nconst rCRLF = /\\r?\\n/g;\n/**\n * Encode a set of form elements as a string for submission.\n *\n * @category Forms\n * @example\n *\n * ```js\n * $('<form><input name=\"foo\" value=\"bar\" /></form>').serialize();\n * //=> 'foo=bar'\n * ```\n *\n * @returns The serialized form.\n * @see {@link https://api.jquery.com/serialize/}\n */\nfunction serialize() {\n    // Convert form elements into name/value objects\n    const arr = this.serializeArray();\n    // Serialize each element into a key/value string\n    const retArr = arr.map((data) => `${encodeURIComponent(data.name)}=${encodeURIComponent(data.value)}`);\n    // Return the resulting serialization\n    return retArr.join('&').replace(r20, '+');\n}\n/**\n * Encode a set of form elements as an array of names and values.\n *\n * @category Forms\n * @example\n *\n * ```js\n * $('<form><input name=\"foo\" value=\"bar\" /></form>').serializeArray();\n * //=> [ { name: 'foo', value: 'bar' } ]\n * ```\n *\n * @returns The serialized form.\n * @see {@link https://api.jquery.com/serializeArray/}\n */\nfunction serializeArray() {\n    // Resolve all form elements from either forms or collections of form elements\n    return this.map((_, elem) => {\n        const $elem = this._make(elem);\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === 'form') {\n            return $elem.find(submittableSelector).toArray();\n        }\n        return $elem.filter(submittableSelector).toArray();\n    })\n        .filter(\n    // Verify elements have a name (`attr.name`) and are not disabled (`:enabled`)\n    '[name!=\"\"]:enabled' +\n        // And cannot be clicked (`[type=submit]`) or are used in `x-www-form-urlencoded` (`[type=file]`)\n        ':not(:submit, :button, :image, :reset, :file)' +\n        // And are either checked/don't have a checkable state\n        ':matches([checked], :not(:checkbox, :radio))')\n        .map((_, elem) => {\n        var _a;\n        const $elem = this._make(elem);\n        const name = $elem.attr('name'); // We have filtered for elements with a name before.\n        // If there is no value set (e.g. `undefined`, `null`), then default value to empty\n        const value = (_a = $elem.val()) !== null && _a !== void 0 ? _a : '';\n        // If we have an array of values (e.g. `<select multiple>`), return an array of key/value pairs\n        if (Array.isArray(value)) {\n            return value.map((val) => \n            /*\n             * We trim replace any line endings (e.g. `\\r` or `\\r\\n` with `\\r\\n`) to guarantee consistency across platforms\n             * These can occur inside of `<textarea>'s`\n             */\n            ({ name, value: val.replace(rCRLF, '\\r\\n') }));\n        }\n        // Otherwise (e.g. `<input type=\"text\">`, return only one key/value pair\n        return { name, value: value.replace(rCRLF, '\\r\\n') };\n    })\n        .toArray();\n}\n//# sourceMappingURL=forms.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/forms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js":
/*!***********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/manipulation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _makeDomArray: () => (/* binding */ _makeDomArray),\n/* harmony export */   after: () => (/* binding */ after),\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   before: () => (/* binding */ before),\n/* harmony export */   clone: () => (/* binding */ clone),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   insertAfter: () => (/* binding */ insertAfter),\n/* harmony export */   insertBefore: () => (/* binding */ insertBefore),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependTo: () => (/* binding */ prependTo),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   replaceWith: () => (/* binding */ replaceWith),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap),\n/* harmony export */   wrapAll: () => (/* binding */ wrapAll),\n/* harmony export */   wrapInner: () => (/* binding */ wrapInner)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parse.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/**\n * Methods for modifying the DOM structure.\n *\n * @module cheerio/manipulation\n */\n\n\n\n\n\n\n/**\n * Create an array of nodes, recursing into arrays and parsing strings if\n * necessary.\n *\n * @private\n * @category Manipulation\n * @param elem - Elements to make an array of.\n * @param clone - Optionally clone nodes.\n * @returns The array of nodes.\n */\nfunction _makeDomArray(elem, clone) {\n    if (elem == null) {\n        return [];\n    }\n    if (typeof elem === 'string') {\n        return this._parse(elem, this.options, false, null).children.slice(0);\n    }\n    if ('length' in elem) {\n        if (elem.length === 1) {\n            return this._makeDomArray(elem[0], clone);\n        }\n        const result = [];\n        for (let i = 0; i < elem.length; i++) {\n            const el = elem[i];\n            if (typeof el === 'object') {\n                if (el == null) {\n                    continue;\n                }\n                if (!('length' in el)) {\n                    result.push(clone ? (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(el, true) : el);\n                    continue;\n                }\n            }\n            result.push(...this._makeDomArray(el, clone));\n        }\n        return result;\n    }\n    return [clone ? (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(elem, true) : elem];\n}\nfunction _insert(concatenator) {\n    return function (...elems) {\n        const lastIdx = this.length - 1;\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n                return;\n            const domSrc = typeof elems[0] === 'function'\n                ? elems[0].call(el, i, this._render(el.children))\n                : elems;\n            const dom = this._makeDomArray(domSrc, i < lastIdx);\n            concatenator(dom, el.children, el);\n        });\n    };\n}\n/**\n * Modify an array in-place, removing some number of elements and adding new\n * elements directly following them.\n *\n * @private\n * @category Manipulation\n * @param array - Target array to splice.\n * @param spliceIdx - Index at which to begin changing the array.\n * @param spliceCount - Number of elements to remove from the array.\n * @param newElems - Elements to insert into the array.\n * @param parent - The parent of the node.\n * @returns The spliced array.\n */\nfunction uniqueSplice(array, spliceIdx, spliceCount, newElems, parent) {\n    var _a, _b;\n    const spliceArgs = [\n        spliceIdx,\n        spliceCount,\n        ...newElems,\n    ];\n    const prev = spliceIdx === 0 ? null : array[spliceIdx - 1];\n    const next = spliceIdx + spliceCount >= array.length\n        ? null\n        : array[spliceIdx + spliceCount];\n    /*\n     * Before splicing in new elements, ensure they do not already appear in the\n     * current array.\n     */\n    for (let idx = 0; idx < newElems.length; ++idx) {\n        const node = newElems[idx];\n        const oldParent = node.parent;\n        if (oldParent) {\n            const oldSiblings = oldParent.children;\n            const prevIdx = oldSiblings.indexOf(node);\n            if (prevIdx !== -1) {\n                oldParent.children.splice(prevIdx, 1);\n                if (parent === oldParent && spliceIdx > prevIdx) {\n                    spliceArgs[0]--;\n                }\n            }\n        }\n        node.parent = parent;\n        if (node.prev) {\n            node.prev.next = (_a = node.next) !== null && _a !== void 0 ? _a : null;\n        }\n        if (node.next) {\n            node.next.prev = (_b = node.prev) !== null && _b !== void 0 ? _b : null;\n        }\n        node.prev = idx === 0 ? prev : newElems[idx - 1];\n        node.next = idx === newElems.length - 1 ? next : newElems[idx + 1];\n    }\n    if (prev) {\n        prev.next = newElems[0];\n    }\n    if (next) {\n        next.prev = newElems[newElems.length - 1];\n    }\n    return array.splice(...spliceArgs);\n}\n/**\n * Insert every element in the set of matched elements to the end of the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').appendTo('#fruits');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //      <li class=\"plum\">Plum</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to append elements to.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/appendTo/}\n */\nfunction appendTo(target) {\n    const appendTarget = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(target) ? target : this._make(target);\n    appendTarget.append(this);\n    return this;\n}\n/**\n * Insert every element in the set of matched elements to the beginning of the\n * target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').prependTo('#fruits');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to prepend elements to.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/prependTo/}\n */\nfunction prependTo(target) {\n    const prependTarget = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(target) ? target : this._make(target);\n    prependTarget.prepend(this);\n    return this;\n}\n/**\n * Inserts content as the _last_ child of each of the selected elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').append('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //      <li class=\"plum\">Plum</li>\n * //    </ul>\n * ```\n *\n * @see {@link https://api.jquery.com/append/}\n */\nconst append = _insert((dom, children, parent) => {\n    uniqueSplice(children, children.length, 0, dom, parent);\n});\n/**\n * Inserts content as the _first_ child of each of the selected elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').prepend('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @see {@link https://api.jquery.com/prepend/}\n */\nconst prepend = _insert((dom, children, parent) => {\n    uniqueSplice(children, 0, 0, dom, parent);\n});\nfunction _wrap(insert) {\n    return function (wrapper) {\n        const lastIdx = this.length - 1;\n        const lastParent = this.parents().last();\n        for (let i = 0; i < this.length; i++) {\n            const el = this[i];\n            const wrap = typeof wrapper === 'function'\n                ? wrapper.call(el, i, el)\n                : typeof wrapper === 'string' && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(wrapper)\n                    ? lastParent.find(wrapper).clone()\n                    : wrapper;\n            const [wrapperDom] = this._makeDomArray(wrap, i < lastIdx);\n            if (!wrapperDom || !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(wrapperDom))\n                continue;\n            let elInsertLocation = wrapperDom;\n            /*\n             * Find the deepest child. Only consider the first tag child of each node\n             * (ignore text); stop if no children are found.\n             */\n            let j = 0;\n            while (j < elInsertLocation.children.length) {\n                const child = elInsertLocation.children[j];\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(child)) {\n                    elInsertLocation = child;\n                    j = 0;\n                }\n                else {\n                    j++;\n                }\n            }\n            insert(el, elInsertLocation, [wrapperDom]);\n        }\n        return this;\n    };\n}\n/**\n * The .wrap() function can take any string or object that could be passed to\n * the $() factory function to specify a DOM structure. This structure may be\n * nested several levels deep, but should contain only one inmost element. A\n * copy of this structure will be wrapped around each of the elements in the set\n * of matched elements. This method returns the original set of elements for\n * chaining purposes.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const redFruit = $('<div class=\"red-fruit\"></div>');\n * $('.apple').wrap(redFruit);\n *\n * //=> <ul id=\"fruits\">\n * //     <div class=\"red-fruit\">\n * //      <li class=\"apple\">Apple</li>\n * //     </div>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"plum\">Plum</li>\n * //   </ul>\n *\n * const healthy = $('<div class=\"healthy\"></div>');\n * $('li').wrap(healthy);\n *\n * //=> <ul id=\"fruits\">\n * //     <div class=\"healthy\">\n * //       <li class=\"apple\">Apple</li>\n * //     </div>\n * //     <div class=\"healthy\">\n * //       <li class=\"orange\">Orange</li>\n * //     </div>\n * //     <div class=\"healthy\">\n * //        <li class=\"plum\">Plum</li>\n * //     </div>\n * //   </ul>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around each element in the\n *   selection.\n * @see {@link https://api.jquery.com/wrap/}\n */\nconst wrap = _wrap((el, elInsertLocation, wrapperDom) => {\n    const { parent } = el;\n    if (!parent)\n        return;\n    const siblings = parent.children;\n    const index = siblings.indexOf(el);\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)([el], elInsertLocation);\n    /*\n     * The previous operation removed the current element from the `siblings`\n     * array, so the `dom` array can be inserted without removing any\n     * additional elements.\n     */\n    uniqueSplice(siblings, index, 0, wrapperDom, parent);\n});\n/**\n * The .wrapInner() function can take any string or object that could be passed\n * to the $() factory function to specify a DOM structure. This structure may be\n * nested several levels deep, but should contain only one inmost element. The\n * structure will be wrapped around the content of each of the elements in the\n * set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const redFruit = $('<div class=\"red-fruit\"></div>');\n * $('.apple').wrapInner(redFruit);\n *\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">\n * //       <div class=\"red-fruit\">Apple</div>\n * //     </li>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"pear\">Pear</li>\n * //   </ul>\n *\n * const healthy = $('<div class=\"healthy\"></div>');\n * $('li').wrapInner(healthy);\n *\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">\n * //       <div class=\"healthy\">Apple</div>\n * //     </li>\n * //     <li class=\"orange\">\n * //       <div class=\"healthy\">Orange</div>\n * //     </li>\n * //     <li class=\"pear\">\n * //       <div class=\"healthy\">Pear</div>\n * //     </li>\n * //   </ul>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around the content of each element\n *   in the selection.\n * @returns The instance itself, for chaining.\n * @see {@link https://api.jquery.com/wrapInner/}\n */\nconst wrapInner = _wrap((el, elInsertLocation, wrapperDom) => {\n    if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n        return;\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(el.children, elInsertLocation);\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(wrapperDom, el);\n});\n/**\n * The .unwrap() function, removes the parents of the set of matched elements\n * from the DOM, leaving the matched elements in their place.\n *\n * @category Manipulation\n * @example <caption>without selector</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div id=test>\\n  <div><p>Hello</p></div>\\n  <div><p>World</p></div>\\n</div>',\n * );\n * $('#test p').unwrap();\n *\n * //=> <div id=test>\n * //     <p>Hello</p>\n * //     <p>World</p>\n * //   </div>\n * ```\n *\n * @example <caption>with selector</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div id=test>\\n  <p>Hello</p>\\n  <b><p>World</p></b>\\n</div>',\n * );\n * $('#test p').unwrap('b');\n *\n * //=> <div id=test>\n * //     <p>Hello</p>\n * //     <p>World</p>\n * //   </div>\n * ```\n *\n * @param selector - A selector to check the parent element against. If an\n *   element's parent does not match the selector, the element won't be\n *   unwrapped.\n * @returns The instance itself, for chaining.\n * @see {@link https://api.jquery.com/unwrap/}\n */\nfunction unwrap(selector) {\n    this.parent(selector)\n        .not('body')\n        .each((_, el) => {\n        this._make(el).replaceWith(el.children);\n    });\n    return this;\n}\n/**\n * The .wrapAll() function can take any string or object that could be passed to\n * the $() function to specify a DOM structure. This structure may be nested\n * several levels deep, but should contain only one inmost element. The\n * structure will be wrapped around all of the elements in the set of matched\n * elements, as a single group.\n *\n * @category Manipulation\n * @example <caption>With markup passed to `wrapAll`</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div class=\"container\"><div class=\"inner\">First</div><div class=\"inner\">Second</div></div>',\n * );\n * $('.inner').wrapAll(\"<div class='new'></div>\");\n *\n * //=> <div class=\"container\">\n * //     <div class='new'>\n * //       <div class=\"inner\">First</div>\n * //       <div class=\"inner\">Second</div>\n * //     </div>\n * //   </div>\n * ```\n *\n * @example <caption>With an existing cheerio instance</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<span>Span 1</span><strong>Strong</strong><span>Span 2</span>',\n * );\n * const wrap = $('<div><p><em><b></b></em></p></div>');\n * $('span').wrapAll(wrap);\n *\n * //=> <div>\n * //     <p>\n * //       <em>\n * //         <b>\n * //           <span>Span 1</span>\n * //           <span>Span 2</span>\n * //         </b>\n * //       </em>\n * //     </p>\n * //   </div>\n * //   <strong>Strong</strong>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around all matched elements in the\n *   selection.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/wrapAll/}\n */\nfunction wrapAll(wrapper) {\n    const el = this[0];\n    if (el) {\n        const wrap = this._make(typeof wrapper === 'function' ? wrapper.call(el, 0, el) : wrapper).insertBefore(el);\n        // If html is given as wrapper, wrap may contain text elements\n        let elInsertLocation;\n        for (let i = 0; i < wrap.length; i++) {\n            if (wrap[i].type === htmlparser2__WEBPACK_IMPORTED_MODULE_5__.ElementType.Tag) {\n                elInsertLocation = wrap[i];\n            }\n        }\n        let j = 0;\n        /*\n         * Find the deepest child. Only consider the first tag child of each node\n         * (ignore text); stop if no children are found.\n         */\n        while (elInsertLocation && j < elInsertLocation.children.length) {\n            const child = elInsertLocation.children[j];\n            if (child.type === htmlparser2__WEBPACK_IMPORTED_MODULE_5__.ElementType.Tag) {\n                elInsertLocation = child;\n                j = 0;\n            }\n            else {\n                j++;\n            }\n        }\n        if (elInsertLocation)\n            this._make(elInsertLocation).append(this);\n    }\n    return this;\n}\n/**\n * Insert content next to each element in the set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.apple').after('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param elems - HTML string, DOM element, array of DOM elements or Cheerio to\n *   insert after each element in the set of matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/after/}\n */\nfunction after(...elems) {\n    const lastIdx = this.length - 1;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el) || !el.parent) {\n            return;\n        }\n        const siblings = el.parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        const domSrc = typeof elems[0] === 'function'\n            ? elems[0].call(el, i, this._render(el.children))\n            : elems;\n        const dom = this._makeDomArray(domSrc, i < lastIdx);\n        // Add element after `this` element\n        uniqueSplice(siblings, index + 1, 0, dom, el.parent);\n    });\n}\n/**\n * Insert every element in the set of matched elements after the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').insertAfter('.apple');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to insert elements after.\n * @returns The set of newly inserted elements.\n * @see {@link https://api.jquery.com/insertAfter/}\n */\nfunction insertAfter(target) {\n    if (typeof target === 'string') {\n        target = this._make(target);\n    }\n    this.remove();\n    const clones = [];\n    for (const el of this._makeDomArray(target)) {\n        const clonedSelf = this.clone().toArray();\n        const { parent } = el;\n        if (!parent) {\n            continue;\n        }\n        const siblings = parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            continue;\n        // Add cloned `this` element(s) after target element\n        uniqueSplice(siblings, index + 1, 0, clonedSelf, parent);\n        clones.push(...clonedSelf);\n    }\n    return this._make(clones);\n}\n/**\n * Insert content previous to each element in the set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.apple').before('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param elems - HTML string, DOM element, array of DOM elements or Cheerio to\n *   insert before each element in the set of matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/before/}\n */\nfunction before(...elems) {\n    const lastIdx = this.length - 1;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el) || !el.parent) {\n            return;\n        }\n        const siblings = el.parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        const domSrc = typeof elems[0] === 'function'\n            ? elems[0].call(el, i, this._render(el.children))\n            : elems;\n        const dom = this._makeDomArray(domSrc, i < lastIdx);\n        // Add element before `el` element\n        uniqueSplice(siblings, index, 0, dom, el.parent);\n    });\n}\n/**\n * Insert every element in the set of matched elements before the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').insertBefore('.apple');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to insert elements before.\n * @returns The set of newly inserted elements.\n * @see {@link https://api.jquery.com/insertBefore/}\n */\nfunction insertBefore(target) {\n    const targetArr = this._make(target);\n    this.remove();\n    const clones = [];\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(targetArr, (el) => {\n        const clonedSelf = this.clone().toArray();\n        const { parent } = el;\n        if (!parent) {\n            return;\n        }\n        const siblings = parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        // Add cloned `this` element(s) after target element\n        uniqueSplice(siblings, index, 0, clonedSelf, parent);\n        clones.push(...clonedSelf);\n    });\n    return this._make(clones);\n}\n/**\n * Removes the set of matched elements from the DOM and all their children.\n * `selector` filters the set of matched elements to be removed.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.pear').remove();\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //    </ul>\n * ```\n *\n * @param selector - Optional selector for elements to remove.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/remove/}\n */\nfunction remove(selector) {\n    // Filter if we have selector\n    const elems = selector ? this.filter(selector) : this;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(elems, (el) => {\n        (0,domutils__WEBPACK_IMPORTED_MODULE_4__.removeElement)(el);\n        el.prev = el.next = el.parent = null;\n    });\n    return this;\n}\n/**\n * Replaces matched elements with `content`.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const plum = $('<li class=\"plum\">Plum</li>');\n * $('.pear').replaceWith(plum);\n * $.html();\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">Apple</li>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"plum\">Plum</li>\n * //   </ul>\n * ```\n *\n * @param content - Replacement for matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/replaceWith/}\n */\nfunction replaceWith(content) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        const { parent } = el;\n        if (!parent) {\n            return;\n        }\n        const siblings = parent.children;\n        const cont = typeof content === 'function' ? content.call(el, i, el) : content;\n        const dom = this._makeDomArray(cont);\n        /*\n         * In the case that `dom` contains nodes that already exist in other\n         * structures, ensure those nodes are properly removed.\n         */\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(dom, null);\n        const index = siblings.indexOf(el);\n        // Completely remove old element\n        uniqueSplice(siblings, index, 1, dom, parent);\n        if (!dom.includes(el)) {\n            el.parent = el.prev = el.next = null;\n        }\n    });\n}\n/**\n * Removes all children from each item in the selection. Text nodes and comment\n * nodes are left as is.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').empty();\n * $.html();\n * //=>  <ul id=\"fruits\"></ul>\n * ```\n *\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/empty/}\n */\nfunction empty() {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        el.children.length = 0;\n    });\n}\nfunction html(str) {\n    if (str === undefined) {\n        const el = this[0];\n        if (!el || !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return null;\n        return this._render(el.children);\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        const content = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(str)\n            ? str.toArray()\n            : this._parse(`${str}`, this.options, false, el).children;\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(content, el);\n    });\n}\n/**\n * Turns the collection to a string. Alias for `.html()`.\n *\n * @category Manipulation\n * @returns The rendered document.\n */\nfunction toString() {\n    return this._render(this);\n}\nfunction text(str) {\n    // If `str` is undefined, act as a \"getter\"\n    if (str === undefined) {\n        return (0,_static_js__WEBPACK_IMPORTED_MODULE_2__.text)(this);\n    }\n    if (typeof str === 'function') {\n        // Function support\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => this._make(el).text(str.call(el, i, (0,_static_js__WEBPACK_IMPORTED_MODULE_2__.text)([el]))));\n    }\n    // Append text node to each selected elements\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        const textNode = new domhandler__WEBPACK_IMPORTED_MODULE_0__.Text(`${str}`);\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(textNode, el);\n    });\n}\n/**\n * Clone the cheerio object.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const moreFruit = $('#fruits').clone();\n * ```\n *\n * @returns The cloned object.\n * @see {@link https://api.jquery.com/clone/}\n */\nfunction clone() {\n    const clone = Array.prototype.map.call(this.get(), (el) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(el, true));\n    // Add a root node around the cloned nodes\n    const root = new domhandler__WEBPACK_IMPORTED_MODULE_0__.Document(clone);\n    for (const node of clone) {\n        node.parent = root;\n    }\n    return this._make(clone);\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js":
/*!*********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/traversing.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _findBySelector: () => (/* binding */ _findBySelector),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   addBack: () => (/* binding */ addBack),\n/* harmony export */   children: () => (/* binding */ children),\n/* harmony export */   closest: () => (/* binding */ closest),\n/* harmony export */   contents: () => (/* binding */ contents),\n/* harmony export */   each: () => (/* binding */ each),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   eq: () => (/* binding */ eq),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   filterArray: () => (/* binding */ filterArray),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   first: () => (/* binding */ first),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   has: () => (/* binding */ has),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   last: () => (/* binding */ last),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   next: () => (/* binding */ next),\n/* harmony export */   nextAll: () => (/* binding */ nextAll),\n/* harmony export */   nextUntil: () => (/* binding */ nextUntil),\n/* harmony export */   not: () => (/* binding */ not),\n/* harmony export */   parent: () => (/* binding */ parent),\n/* harmony export */   parents: () => (/* binding */ parents),\n/* harmony export */   parentsUntil: () => (/* binding */ parentsUntil),\n/* harmony export */   prev: () => (/* binding */ prev),\n/* harmony export */   prevAll: () => (/* binding */ prevAll),\n/* harmony export */   prevUntil: () => (/* binding */ prevUntil),\n/* harmony export */   siblings: () => (/* binding */ siblings),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var cheerio_select__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cheerio-select */ \"(rsc)/./node_modules/cheerio-select/lib/esm/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/**\n * Methods for traversing the DOM structure.\n *\n * @module cheerio/traversing\n */\n\n\n\n\n\nconst reSiblingSelector = /^\\s*[+~]/;\n/**\n * Get the descendants of each element in the current set of matched elements,\n * filtered by a selector, jQuery object, or element.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').find('li').length;\n * //=> 3\n * $('#fruits').find($('.apple')).length;\n * //=> 1\n * ```\n *\n * @param selectorOrHaystack - Element to look for.\n * @returns The found elements.\n * @see {@link https://api.jquery.com/find/}\n */\nfunction find(selectorOrHaystack) {\n    if (!selectorOrHaystack) {\n        return this._make([]);\n    }\n    if (typeof selectorOrHaystack !== 'string') {\n        const haystack = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(selectorOrHaystack)\n            ? selectorOrHaystack.toArray()\n            : [selectorOrHaystack];\n        const context = this.toArray();\n        return this._make(haystack.filter((elem) => context.some((node) => (0,_static_js__WEBPACK_IMPORTED_MODULE_3__.contains)(node, elem))));\n    }\n    return this._findBySelector(selectorOrHaystack, Number.POSITIVE_INFINITY);\n}\n/**\n * Find elements by a specific selector.\n *\n * @private\n * @category Traversing\n * @param selector - Selector to filter by.\n * @param limit - Maximum number of elements to match.\n * @returns The found elements.\n */\nfunction _findBySelector(selector, limit) {\n    var _a;\n    const context = this.toArray();\n    const elems = reSiblingSelector.test(selector)\n        ? context\n        : this.children().toArray();\n    const options = {\n        context,\n        root: (_a = this._root) === null || _a === void 0 ? void 0 : _a[0],\n        // Pass options that are recognized by `cheerio-select`\n        xmlMode: this.options.xmlMode,\n        lowerCaseTags: this.options.lowerCaseTags,\n        lowerCaseAttributeNames: this.options.lowerCaseAttributeNames,\n        pseudos: this.options.pseudos,\n        quirksMode: this.options.quirksMode,\n    };\n    return this._make(cheerio_select__WEBPACK_IMPORTED_MODULE_1__.select(selector, elems, options, limit));\n}\n/**\n * Creates a matcher, using a particular mapping function. Matchers provide a\n * function that finds elements using a generating function, supporting\n * filtering.\n *\n * @private\n * @param matchMap - Mapping function.\n * @returns - Function for wrapping generating functions.\n */\nfunction _getMatcher(matchMap) {\n    return function (fn, ...postFns) {\n        return function (selector) {\n            var _a;\n            let matched = matchMap(fn, this);\n            if (selector) {\n                matched = filterArray(matched, selector, this.options.xmlMode, (_a = this._root) === null || _a === void 0 ? void 0 : _a[0]);\n            }\n            return this._make(\n            // Post processing is only necessary if there is more than one element.\n            this.length > 1 && matched.length > 1\n                ? postFns.reduce((elems, fn) => fn(elems), matched)\n                : matched);\n        };\n    };\n}\n/** Matcher that adds multiple elements for each entry in the input. */\nconst _matcher = _getMatcher((fn, elems) => {\n    let ret = [];\n    for (let i = 0; i < elems.length; i++) {\n        const value = fn(elems[i]);\n        if (value.length > 0)\n            ret = ret.concat(value);\n    }\n    return ret;\n});\n/** Matcher that adds at most one element for each entry in the input. */\nconst _singleMatcher = _getMatcher((fn, elems) => {\n    const ret = [];\n    for (let i = 0; i < elems.length; i++) {\n        const value = fn(elems[i]);\n        if (value !== null) {\n            ret.push(value);\n        }\n    }\n    return ret;\n});\n/**\n * Matcher that supports traversing until a condition is met.\n *\n * @param nextElem - Function that returns the next element.\n * @param postFns - Post processing functions.\n * @returns A function usable for `*Until` methods.\n */\nfunction _matchUntil(nextElem, ...postFns) {\n    // We use a variable here that is used from within the matcher.\n    let matches = null;\n    const innerMatcher = _getMatcher((nextElem, elems) => {\n        const matched = [];\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.domEach)(elems, (elem) => {\n            for (let next; (next = nextElem(elem)); elem = next) {\n                // FIXME: `matched` might contain duplicates here and the index is too large.\n                if (matches === null || matches === void 0 ? void 0 : matches(next, matched.length))\n                    break;\n                matched.push(next);\n            }\n        });\n        return matched;\n    })(nextElem, ...postFns);\n    return function (selector, filterSelector) {\n        // Override `matches` variable with the new target.\n        matches =\n            typeof selector === 'string'\n                ? (elem) => cheerio_select__WEBPACK_IMPORTED_MODULE_1__.is(elem, selector, this.options)\n                : selector\n                    ? getFilterFn(selector)\n                    : null;\n        const ret = innerMatcher.call(this, filterSelector);\n        // Set `matches` to `null`, so we don't waste memory.\n        matches = null;\n        return ret;\n    };\n}\nfunction _removeDuplicates(elems) {\n    return elems.length > 1 ? Array.from(new Set(elems)) : elems;\n}\n/**\n * Get the parent of each element in the current set of matched elements,\n * optionally filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').parent().attr('id');\n * //=> fruits\n * ```\n *\n * @param selector - If specified filter for parent.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parent/}\n */\nconst parent = _singleMatcher(({ parent }) => (parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(parent) ? parent : null), _removeDuplicates);\n/**\n * Get a set of parents filtered by `selector` of each element in the current\n * set of match elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').parents().length;\n * //=> 2\n * $('.orange').parents('#fruits').length;\n * //=> 1\n * ```\n *\n * @param selector - If specified filter for parents.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parents/}\n */\nconst parents = _matcher((elem) => {\n    const matched = [];\n    while (elem.parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(elem.parent)) {\n        matched.push(elem.parent);\n        elem = elem.parent;\n    }\n    return matched;\n}, domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort, (elems) => elems.reverse());\n/**\n * Get the ancestors of each element in the current set of matched elements, up\n * to but not including the element matched by the selector, DOM node, or\n * cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').parentsUntil('#food').length;\n * //=> 1\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - Optional filter for parents.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parentsUntil/}\n */\nconst parentsUntil = _matchUntil(({ parent }) => (parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(parent) ? parent : null), domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort, (elems) => elems.reverse());\n/**\n * For each element in the set, get the first element that matches the selector\n * by testing the element itself and traversing up through its ancestors in the\n * DOM tree.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').closest();\n * //=> []\n *\n * $('.orange').closest('.apple');\n * // => []\n *\n * $('.orange').closest('li');\n * //=> [<li class=\"orange\">Orange</li>]\n *\n * $('.orange').closest('#fruits');\n * //=> [<ul id=\"fruits\"> ... </ul>]\n * ```\n *\n * @param selector - Selector for the element to find.\n * @returns The closest nodes.\n * @see {@link https://api.jquery.com/closest/}\n */\nfunction closest(selector) {\n    var _a;\n    const set = [];\n    if (!selector) {\n        return this._make(set);\n    }\n    const selectOpts = {\n        xmlMode: this.options.xmlMode,\n        root: (_a = this._root) === null || _a === void 0 ? void 0 : _a[0],\n    };\n    const selectFn = typeof selector === 'string'\n        ? (elem) => cheerio_select__WEBPACK_IMPORTED_MODULE_1__.is(elem, selector, selectOpts)\n        : getFilterFn(selector);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.domEach)(this, (elem) => {\n        if (elem && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(elem) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem)) {\n            elem = elem.parent;\n        }\n        while (elem && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem)) {\n            if (selectFn(elem, 0)) {\n                // Do not add duplicate elements to the set\n                if (!set.includes(elem)) {\n                    set.push(elem);\n                }\n                break;\n            }\n            elem = elem.parent;\n        }\n    });\n    return this._make(set);\n}\n/**\n * Gets the next sibling of each selected element, optionally filtered by a\n * selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').next().hasClass('orange');\n * //=> true\n * ```\n *\n * @param selector - If specified filter for sibling.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/next/}\n */\nconst next = _singleMatcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.nextElementSibling)(elem));\n/**\n * Gets all the following siblings of the each selected element, optionally\n * filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').nextAll();\n * //=> [<li class=\"orange\">Orange</li>, <li class=\"pear\">Pear</li>]\n * $('.apple').nextAll('.orange');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/nextAll/}\n */\nconst nextAll = _matcher((elem) => {\n    const matched = [];\n    while (elem.next) {\n        elem = elem.next;\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem))\n            matched.push(elem);\n    }\n    return matched;\n}, _removeDuplicates);\n/**\n * Gets all the following siblings up to but not including the element matched\n * by the selector, optionally filtered by another selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').nextUntil('.pear');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - If specified filter for siblings.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/nextUntil/}\n */\nconst nextUntil = _matchUntil((el) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.nextElementSibling)(el), _removeDuplicates);\n/**\n * Gets the previous sibling of each selected element optionally filtered by a\n * selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').prev().hasClass('apple');\n * //=> true\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prev/}\n */\nconst prev = _singleMatcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.prevElementSibling)(elem));\n/**\n * Gets all the preceding siblings of each selected element, optionally filtered\n * by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').prevAll();\n * //=> [<li class=\"orange\">Orange</li>, <li class=\"apple\">Apple</li>]\n *\n * $('.pear').prevAll('.orange');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prevAll/}\n */\nconst prevAll = _matcher((elem) => {\n    const matched = [];\n    while (elem.prev) {\n        elem = elem.prev;\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem))\n            matched.push(elem);\n    }\n    return matched;\n}, _removeDuplicates);\n/**\n * Gets all the preceding siblings up to but not including the element matched\n * by the selector, optionally filtered by another selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').prevUntil('.apple');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prevUntil/}\n */\nconst prevUntil = _matchUntil((el) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.prevElementSibling)(el), _removeDuplicates);\n/**\n * Get the siblings of each element (excluding the element) in the set of\n * matched elements, optionally filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').siblings().length;\n * //=> 2\n *\n * $('.pear').siblings('.orange').length;\n * //=> 1\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The siblings.\n * @see {@link https://api.jquery.com/siblings/}\n */\nconst siblings = _matcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getSiblings)(elem).filter((el) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(el) && el !== elem), domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort);\n/**\n * Gets the element children of each element in the set of matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().length;\n * //=> 3\n *\n * $('#fruits').children('.pear').text();\n * //=> Pear\n * ```\n *\n * @param selector - If specified filter for children.\n * @returns The children.\n * @see {@link https://api.jquery.com/children/}\n */\nconst children = _matcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getChildren)(elem).filter(domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag), _removeDuplicates);\n/**\n * Gets the children of each element in the set of matched elements, including\n * text and comment nodes.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').contents().length;\n * //=> 3\n * ```\n *\n * @returns The children.\n * @see {@link https://api.jquery.com/contents/}\n */\nfunction contents() {\n    const elems = this.toArray().reduce((newElems, elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? newElems.concat(elem.children) : newElems, []);\n    return this._make(elems);\n}\n/**\n * Iterates over a cheerio object, executing a function for each matched\n * element. When the callback is fired, the function is fired in the context of\n * the DOM element, so `this` refers to the current element, which is equivalent\n * to the function parameter `element`. To break out of the `each` loop early,\n * return with `false`.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * const fruits = [];\n *\n * $('li').each(function (i, elem) {\n *   fruits[i] = $(this).text();\n * });\n *\n * fruits.join(', ');\n * //=> Apple, Orange, Pear\n * ```\n *\n * @param fn - Function to execute.\n * @returns The instance itself, useful for chaining.\n * @see {@link https://api.jquery.com/each/}\n */\nfunction each(fn) {\n    let i = 0;\n    const len = this.length;\n    while (i < len && fn.call(this[i], i, this[i]) !== false)\n        ++i;\n    return this;\n}\n/**\n * Pass each element in the current matched set through a function, producing a\n * new Cheerio object containing the return values. The function can return an\n * individual data item or an array of data items to be inserted into the\n * resulting set. If an array is returned, the elements inside the array are\n * inserted into the set. If the function returns null or undefined, no element\n * will be inserted.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li')\n *   .map(function (i, el) {\n *     // this === el\n *     return $(this).text();\n *   })\n *   .toArray()\n *   .join(' ');\n * //=> \"apple orange pear\"\n * ```\n *\n * @param fn - Function to execute.\n * @returns The mapped elements, wrapped in a Cheerio collection.\n * @see {@link https://api.jquery.com/map/}\n */\nfunction map(fn) {\n    let elems = [];\n    for (let i = 0; i < this.length; i++) {\n        const el = this[i];\n        const val = fn.call(el, i, el);\n        if (val != null) {\n            elems = elems.concat(val);\n        }\n    }\n    return this._make(elems);\n}\n/**\n * Creates a function to test if a filter is matched.\n *\n * @param match - A filter.\n * @returns A function that determines if a filter has been matched.\n */\nfunction getFilterFn(match) {\n    if (typeof match === 'function') {\n        return (el, i) => match.call(el, i, el);\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(match)) {\n        return (el) => Array.prototype.includes.call(match, el);\n    }\n    return function (el) {\n        return match === el;\n    };\n}\nfunction filter(match) {\n    var _a;\n    return this._make(filterArray(this.toArray(), match, this.options.xmlMode, (_a = this._root) === null || _a === void 0 ? void 0 : _a[0]));\n}\nfunction filterArray(nodes, match, xmlMode, root) {\n    return typeof match === 'string'\n        ? cheerio_select__WEBPACK_IMPORTED_MODULE_1__.filter(match, nodes, { xmlMode, root })\n        : nodes.filter(getFilterFn(match));\n}\n/**\n * Checks the current list of elements and returns `true` if _any_ of the\n * elements match the selector. If using an element or Cheerio selection,\n * returns `true` if _any_ of the elements match. If using a predicate function,\n * the function is executed in the context of the selected element, so `this`\n * refers to the current element.\n *\n * @category Traversing\n * @param selector - Selector for the selection.\n * @returns Whether or not the selector matches an element of the instance.\n * @see {@link https://api.jquery.com/is/}\n */\nfunction is(selector) {\n    const nodes = this.toArray();\n    return typeof selector === 'string'\n        ? cheerio_select__WEBPACK_IMPORTED_MODULE_1__.some(nodes.filter(domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag), selector, this.options)\n        : selector\n            ? nodes.some(getFilterFn(selector))\n            : false;\n}\n/**\n * Remove elements from the set of matched elements. Given a Cheerio object that\n * represents a set of DOM elements, the `.not()` method constructs a new\n * Cheerio object from a subset of the matching elements. The supplied selector\n * is tested against each element; the elements that don't match the selector\n * will be included in the result.\n *\n * The `.not()` method can take a function as its argument in the same way that\n * `.filter()` does. Elements for which the function returns `true` are excluded\n * from the filtered set; all other elements are included.\n *\n * @category Traversing\n * @example <caption>Selector</caption>\n *\n * ```js\n * $('li').not('.apple').length;\n * //=> 2\n * ```\n *\n * @example <caption>Function</caption>\n *\n * ```js\n * $('li').not(function (i, el) {\n *   // this === el\n *   return $(this).attr('class') === 'orange';\n * }).length; //=> 2\n * ```\n *\n * @param match - Value to look for, following the rules above.\n * @returns The filtered collection.\n * @see {@link https://api.jquery.com/not/}\n */\nfunction not(match) {\n    let nodes = this.toArray();\n    if (typeof match === 'string') {\n        const matches = new Set(cheerio_select__WEBPACK_IMPORTED_MODULE_1__.filter(match, nodes, this.options));\n        nodes = nodes.filter((el) => !matches.has(el));\n    }\n    else {\n        const filterFn = getFilterFn(match);\n        nodes = nodes.filter((el, i) => !filterFn(el, i));\n    }\n    return this._make(nodes);\n}\n/**\n * Filters the set of matched elements to only those which have the given DOM\n * element as a descendant or which have a descendant that matches the given\n * selector. Equivalent to `.filter(':has(selector)')`.\n *\n * @category Traversing\n * @example <caption>Selector</caption>\n *\n * ```js\n * $('ul').has('.pear').attr('id');\n * //=> fruits\n * ```\n *\n * @example <caption>Element</caption>\n *\n * ```js\n * $('ul').has($('.pear')[0]).attr('id');\n * //=> fruits\n * ```\n *\n * @param selectorOrHaystack - Element to look for.\n * @returns The filtered collection.\n * @see {@link https://api.jquery.com/has/}\n */\nfunction has(selectorOrHaystack) {\n    return this.filter(typeof selectorOrHaystack === 'string'\n        ? // Using the `:has` selector here short-circuits searches.\n            `:has(${selectorOrHaystack})`\n        : (_, el) => this._make(el).find(selectorOrHaystack).length > 0);\n}\n/**\n * Will select the first element of a cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().first().text();\n * //=> Apple\n * ```\n *\n * @returns The first element.\n * @see {@link https://api.jquery.com/first/}\n */\nfunction first() {\n    return this.length > 1 ? this._make(this[0]) : this;\n}\n/**\n * Will select the last element of a cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().last().text();\n * //=> Pear\n * ```\n *\n * @returns The last element.\n * @see {@link https://api.jquery.com/last/}\n */\nfunction last() {\n    return this.length > 0 ? this._make(this[this.length - 1]) : this;\n}\n/**\n * Reduce the set of matched elements to the one at the specified index. Use\n * `.eq(-i)` to count backwards from the last selected element.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).text();\n * //=> Apple\n *\n * $('li').eq(-1).text();\n * //=> Pear\n * ```\n *\n * @param i - Index of the element to select.\n * @returns The element at the `i`th position.\n * @see {@link https://api.jquery.com/eq/}\n */\nfunction eq(i) {\n    var _a;\n    i = +i;\n    // Use the first identity optimization if possible\n    if (i === 0 && this.length <= 1)\n        return this;\n    if (i < 0)\n        i = this.length + i;\n    return this._make((_a = this[i]) !== null && _a !== void 0 ? _a : []);\n}\nfunction get(i) {\n    if (i == null) {\n        return this.toArray();\n    }\n    return this[i < 0 ? this.length + i : i];\n}\n/**\n * Retrieve all the DOM elements contained in the jQuery set as an array.\n *\n * @example\n *\n * ```js\n * $('li').toArray();\n * //=> [ {...}, {...}, {...} ]\n * ```\n *\n * @returns The contained items.\n */\nfunction toArray() {\n    return Array.prototype.slice.call(this);\n}\n/**\n * Search for a given element from among the matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').index();\n * //=> 2 $('.orange').index('li');\n * //=> 1\n * $('.apple').index($('#fruit, li'));\n * //=> 1\n * ```\n *\n * @param selectorOrNeedle - Element to look for.\n * @returns The index of the element.\n * @see {@link https://api.jquery.com/index/}\n */\nfunction index(selectorOrNeedle) {\n    let $haystack;\n    let needle;\n    if (selectorOrNeedle == null) {\n        $haystack = this.parent().children();\n        needle = this[0];\n    }\n    else if (typeof selectorOrNeedle === 'string') {\n        $haystack = this._make(selectorOrNeedle);\n        needle = this[0];\n    }\n    else {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias, unicorn/no-this-assignment\n        $haystack = this;\n        needle = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(selectorOrNeedle)\n            ? selectorOrNeedle[0]\n            : selectorOrNeedle;\n    }\n    return Array.prototype.indexOf.call($haystack, needle);\n}\n/**\n * Gets the elements matching the specified range (0-based position).\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').slice(1).eq(0).text();\n * //=> 'Orange'\n *\n * $('li').slice(1, 2).length;\n * //=> 1\n * ```\n *\n * @param start - A position at which the elements begin to be selected. If\n *   negative, it indicates an offset from the end of the set.\n * @param end - A position at which the elements stop being selected. If\n *   negative, it indicates an offset from the end of the set. If omitted, the\n *   range continues until the end of the set.\n * @returns The elements matching the specified range.\n * @see {@link https://api.jquery.com/slice/}\n */\nfunction slice(start, end) {\n    return this._make(Array.prototype.slice.call(this, start, end));\n}\n/**\n * End the most recent filtering operation in the current chain and return the\n * set of matched elements to its previous state.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).end().length;\n * //=> 3\n * ```\n *\n * @returns The previous state of the set of matched elements.\n * @see {@link https://api.jquery.com/end/}\n */\nfunction end() {\n    var _a;\n    return (_a = this.prevObject) !== null && _a !== void 0 ? _a : this._make([]);\n}\n/**\n * Add elements to the set of matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').add('.orange').length;\n * //=> 2\n * ```\n *\n * @param other - Elements to add.\n * @param context - Optionally the context of the new selection.\n * @returns The combined set.\n * @see {@link https://api.jquery.com/add/}\n */\nfunction add(other, context) {\n    const selection = this._make(other, context);\n    const contents = (0,domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort)([...this.get(), ...selection.get()]);\n    return this._make(contents);\n}\n/**\n * Add the previous set of elements on the stack to the current set, optionally\n * filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).addBack('.orange').length;\n * //=> 2\n * ```\n *\n * @param selector - Selector for the elements to add.\n * @returns The combined set.\n * @see {@link https://api.jquery.com/addBack/}\n */\nfunction addBack(selector) {\n    return this.prevObject\n        ? this.add(selector ? this.prevObject.filter(selector) : this.prevObject)\n        : this;\n}\n//# sourceMappingURL=traversing.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY2hlZXJpby9kaXN0L2VzbS9hcGkvdHJhdmVyc2luZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUM2RDtBQUNwQjtBQUNRO0FBQ1Q7QUFDaUU7QUFDekc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsb0RBQVM7QUFDbEM7QUFDQTtBQUNBO0FBQ0EsMkVBQTJFLG9EQUFRO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGtEQUFhO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isa0JBQWtCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxrREFBTztBQUNmLDJCQUEyQix5QkFBeUI7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw4Q0FBUztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ08saUNBQWlDLFFBQVEsaUJBQWlCLHNEQUFVO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBLDJCQUEyQixzREFBVTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBRSxnREFBVTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ08sb0NBQW9DLFFBQVEsaUJBQWlCLHNEQUFVLDJCQUEyQixnREFBVTtBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDhDQUFTO0FBQzdCO0FBQ0EsSUFBSSxrREFBTztBQUNYLHFCQUFxQixzREFBVSxXQUFXLGlEQUFLO0FBQy9DO0FBQ0E7QUFDQSx1QkFBdUIsaURBQUs7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ08sc0NBQXNDLDREQUFrQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsWUFBWSxpREFBSztBQUNqQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ08sc0NBQXNDLDREQUFrQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ08sc0NBQXNDLDREQUFrQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlEQUFLO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDTyxzQ0FBc0MsNERBQWtCO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDTyxvQ0FBb0MscURBQVcsc0JBQXNCLGlEQUFLLHNCQUFzQixnREFBVTtBQUNqSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPLG9DQUFvQyxxREFBVyxjQUFjLDZDQUFLO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUCw0REFBNEQsdURBQVc7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDTztBQUNQO0FBQ0Esb0JBQW9CLGlCQUFpQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9EQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLFVBQVUsa0RBQWEsaUJBQWlCLGVBQWU7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0EsVUFBVSxnREFBVyxjQUFjLDZDQUFLO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxVQUFVO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDTztBQUNQO0FBQ0E7QUFDQSxnQ0FBZ0Msa0RBQWE7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFtQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLElBQUksR0FBRyxJQUFJLEdBQUcsS0FBSztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixvREFBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ087QUFDUDtBQUNBLHFCQUFxQixvREFBVTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEb2N1bWVudHNcXDNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGNoZWVyaW9cXGRpc3RcXGVzbVxcYXBpXFx0cmF2ZXJzaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWV0aG9kcyBmb3IgdHJhdmVyc2luZyB0aGUgRE9NIHN0cnVjdHVyZS5cbiAqXG4gKiBAbW9kdWxlIGNoZWVyaW8vdHJhdmVyc2luZ1xuICovXG5pbXBvcnQgeyBpc1RhZywgaGFzQ2hpbGRyZW4sIGlzRG9jdW1lbnQsIH0gZnJvbSAnZG9taGFuZGxlcic7XG5pbXBvcnQgKiBhcyBzZWxlY3QgZnJvbSAnY2hlZXJpby1zZWxlY3QnO1xuaW1wb3J0IHsgZG9tRWFjaCwgaXNDaGVlcmlvIH0gZnJvbSAnLi4vdXRpbHMuanMnO1xuaW1wb3J0IHsgY29udGFpbnMgfSBmcm9tICcuLi9zdGF0aWMuanMnO1xuaW1wb3J0IHsgZ2V0Q2hpbGRyZW4sIGdldFNpYmxpbmdzLCBuZXh0RWxlbWVudFNpYmxpbmcsIHByZXZFbGVtZW50U2libGluZywgdW5pcXVlU29ydCwgfSBmcm9tICdkb211dGlscyc7XG5jb25zdCByZVNpYmxpbmdTZWxlY3RvciA9IC9eXFxzKlsrfl0vO1xuLyoqXG4gKiBHZXQgdGhlIGRlc2NlbmRhbnRzIG9mIGVhY2ggZWxlbWVudCBpbiB0aGUgY3VycmVudCBzZXQgb2YgbWF0Y2hlZCBlbGVtZW50cyxcbiAqIGZpbHRlcmVkIGJ5IGEgc2VsZWN0b3IsIGpRdWVyeSBvYmplY3QsIG9yIGVsZW1lbnQuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJyNmcnVpdHMnKS5maW5kKCdsaScpLmxlbmd0aDtcbiAqIC8vPT4gM1xuICogJCgnI2ZydWl0cycpLmZpbmQoJCgnLmFwcGxlJykpLmxlbmd0aDtcbiAqIC8vPT4gMVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yT3JIYXlzdGFjayAtIEVsZW1lbnQgdG8gbG9vayBmb3IuXG4gKiBAcmV0dXJucyBUaGUgZm91bmQgZWxlbWVudHMuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL2ZpbmQvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZmluZChzZWxlY3Rvck9ySGF5c3RhY2spIHtcbiAgICBpZiAoIXNlbGVjdG9yT3JIYXlzdGFjaykge1xuICAgICAgICByZXR1cm4gdGhpcy5fbWFrZShbXSk7XG4gICAgfVxuICAgIGlmICh0eXBlb2Ygc2VsZWN0b3JPckhheXN0YWNrICE9PSAnc3RyaW5nJykge1xuICAgICAgICBjb25zdCBoYXlzdGFjayA9IGlzQ2hlZXJpbyhzZWxlY3Rvck9ySGF5c3RhY2spXG4gICAgICAgICAgICA/IHNlbGVjdG9yT3JIYXlzdGFjay50b0FycmF5KClcbiAgICAgICAgICAgIDogW3NlbGVjdG9yT3JIYXlzdGFja107XG4gICAgICAgIGNvbnN0IGNvbnRleHQgPSB0aGlzLnRvQXJyYXkoKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX21ha2UoaGF5c3RhY2suZmlsdGVyKChlbGVtKSA9PiBjb250ZXh0LnNvbWUoKG5vZGUpID0+IGNvbnRhaW5zKG5vZGUsIGVsZW0pKSkpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fZmluZEJ5U2VsZWN0b3Ioc2VsZWN0b3JPckhheXN0YWNrLCBOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFkpO1xufVxuLyoqXG4gKiBGaW5kIGVsZW1lbnRzIGJ5IGEgc3BlY2lmaWMgc2VsZWN0b3IuXG4gKlxuICogQHByaXZhdGVcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAcGFyYW0gc2VsZWN0b3IgLSBTZWxlY3RvciB0byBmaWx0ZXIgYnkuXG4gKiBAcGFyYW0gbGltaXQgLSBNYXhpbXVtIG51bWJlciBvZiBlbGVtZW50cyB0byBtYXRjaC5cbiAqIEByZXR1cm5zIFRoZSBmb3VuZCBlbGVtZW50cy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIF9maW5kQnlTZWxlY3RvcihzZWxlY3RvciwgbGltaXQpIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgY29udGV4dCA9IHRoaXMudG9BcnJheSgpO1xuICAgIGNvbnN0IGVsZW1zID0gcmVTaWJsaW5nU2VsZWN0b3IudGVzdChzZWxlY3RvcilcbiAgICAgICAgPyBjb250ZXh0XG4gICAgICAgIDogdGhpcy5jaGlsZHJlbigpLnRvQXJyYXkoKTtcbiAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICBjb250ZXh0LFxuICAgICAgICByb290OiAoX2EgPSB0aGlzLl9yb290KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2FbMF0sXG4gICAgICAgIC8vIFBhc3Mgb3B0aW9ucyB0aGF0IGFyZSByZWNvZ25pemVkIGJ5IGBjaGVlcmlvLXNlbGVjdGBcbiAgICAgICAgeG1sTW9kZTogdGhpcy5vcHRpb25zLnhtbE1vZGUsXG4gICAgICAgIGxvd2VyQ2FzZVRhZ3M6IHRoaXMub3B0aW9ucy5sb3dlckNhc2VUYWdzLFxuICAgICAgICBsb3dlckNhc2VBdHRyaWJ1dGVOYW1lczogdGhpcy5vcHRpb25zLmxvd2VyQ2FzZUF0dHJpYnV0ZU5hbWVzLFxuICAgICAgICBwc2V1ZG9zOiB0aGlzLm9wdGlvbnMucHNldWRvcyxcbiAgICAgICAgcXVpcmtzTW9kZTogdGhpcy5vcHRpb25zLnF1aXJrc01vZGUsXG4gICAgfTtcbiAgICByZXR1cm4gdGhpcy5fbWFrZShzZWxlY3Quc2VsZWN0KHNlbGVjdG9yLCBlbGVtcywgb3B0aW9ucywgbGltaXQpKTtcbn1cbi8qKlxuICogQ3JlYXRlcyBhIG1hdGNoZXIsIHVzaW5nIGEgcGFydGljdWxhciBtYXBwaW5nIGZ1bmN0aW9uLiBNYXRjaGVycyBwcm92aWRlIGFcbiAqIGZ1bmN0aW9uIHRoYXQgZmluZHMgZWxlbWVudHMgdXNpbmcgYSBnZW5lcmF0aW5nIGZ1bmN0aW9uLCBzdXBwb3J0aW5nXG4gKiBmaWx0ZXJpbmcuXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSBtYXRjaE1hcCAtIE1hcHBpbmcgZnVuY3Rpb24uXG4gKiBAcmV0dXJucyAtIEZ1bmN0aW9uIGZvciB3cmFwcGluZyBnZW5lcmF0aW5nIGZ1bmN0aW9ucy5cbiAqL1xuZnVuY3Rpb24gX2dldE1hdGNoZXIobWF0Y2hNYXApIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKGZuLCAuLi5wb3N0Rm5zKSB7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiAoc2VsZWN0b3IpIHtcbiAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgIGxldCBtYXRjaGVkID0gbWF0Y2hNYXAoZm4sIHRoaXMpO1xuICAgICAgICAgICAgaWYgKHNlbGVjdG9yKSB7XG4gICAgICAgICAgICAgICAgbWF0Y2hlZCA9IGZpbHRlckFycmF5KG1hdGNoZWQsIHNlbGVjdG9yLCB0aGlzLm9wdGlvbnMueG1sTW9kZSwgKF9hID0gdGhpcy5fcm9vdCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hWzBdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9tYWtlKFxuICAgICAgICAgICAgLy8gUG9zdCBwcm9jZXNzaW5nIGlzIG9ubHkgbmVjZXNzYXJ5IGlmIHRoZXJlIGlzIG1vcmUgdGhhbiBvbmUgZWxlbWVudC5cbiAgICAgICAgICAgIHRoaXMubGVuZ3RoID4gMSAmJiBtYXRjaGVkLmxlbmd0aCA+IDFcbiAgICAgICAgICAgICAgICA/IHBvc3RGbnMucmVkdWNlKChlbGVtcywgZm4pID0+IGZuKGVsZW1zKSwgbWF0Y2hlZClcbiAgICAgICAgICAgICAgICA6IG1hdGNoZWQpO1xuICAgICAgICB9O1xuICAgIH07XG59XG4vKiogTWF0Y2hlciB0aGF0IGFkZHMgbXVsdGlwbGUgZWxlbWVudHMgZm9yIGVhY2ggZW50cnkgaW4gdGhlIGlucHV0LiAqL1xuY29uc3QgX21hdGNoZXIgPSBfZ2V0TWF0Y2hlcigoZm4sIGVsZW1zKSA9PiB7XG4gICAgbGV0IHJldCA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZWxlbXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBmbihlbGVtc1tpXSk7XG4gICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiAwKVxuICAgICAgICAgICAgcmV0ID0gcmV0LmNvbmNhdCh2YWx1ZSk7XG4gICAgfVxuICAgIHJldHVybiByZXQ7XG59KTtcbi8qKiBNYXRjaGVyIHRoYXQgYWRkcyBhdCBtb3N0IG9uZSBlbGVtZW50IGZvciBlYWNoIGVudHJ5IGluIHRoZSBpbnB1dC4gKi9cbmNvbnN0IF9zaW5nbGVNYXRjaGVyID0gX2dldE1hdGNoZXIoKGZuLCBlbGVtcykgPT4ge1xuICAgIGNvbnN0IHJldCA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZWxlbXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBmbihlbGVtc1tpXSk7XG4gICAgICAgIGlmICh2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgcmV0LnB1c2godmFsdWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXQ7XG59KTtcbi8qKlxuICogTWF0Y2hlciB0aGF0IHN1cHBvcnRzIHRyYXZlcnNpbmcgdW50aWwgYSBjb25kaXRpb24gaXMgbWV0LlxuICpcbiAqIEBwYXJhbSBuZXh0RWxlbSAtIEZ1bmN0aW9uIHRoYXQgcmV0dXJucyB0aGUgbmV4dCBlbGVtZW50LlxuICogQHBhcmFtIHBvc3RGbnMgLSBQb3N0IHByb2Nlc3NpbmcgZnVuY3Rpb25zLlxuICogQHJldHVybnMgQSBmdW5jdGlvbiB1c2FibGUgZm9yIGAqVW50aWxgIG1ldGhvZHMuXG4gKi9cbmZ1bmN0aW9uIF9tYXRjaFVudGlsKG5leHRFbGVtLCAuLi5wb3N0Rm5zKSB7XG4gICAgLy8gV2UgdXNlIGEgdmFyaWFibGUgaGVyZSB0aGF0IGlzIHVzZWQgZnJvbSB3aXRoaW4gdGhlIG1hdGNoZXIuXG4gICAgbGV0IG1hdGNoZXMgPSBudWxsO1xuICAgIGNvbnN0IGlubmVyTWF0Y2hlciA9IF9nZXRNYXRjaGVyKChuZXh0RWxlbSwgZWxlbXMpID0+IHtcbiAgICAgICAgY29uc3QgbWF0Y2hlZCA9IFtdO1xuICAgICAgICBkb21FYWNoKGVsZW1zLCAoZWxlbSkgPT4ge1xuICAgICAgICAgICAgZm9yIChsZXQgbmV4dDsgKG5leHQgPSBuZXh0RWxlbShlbGVtKSk7IGVsZW0gPSBuZXh0KSB7XG4gICAgICAgICAgICAgICAgLy8gRklYTUU6IGBtYXRjaGVkYCBtaWdodCBjb250YWluIGR1cGxpY2F0ZXMgaGVyZSBhbmQgdGhlIGluZGV4IGlzIHRvbyBsYXJnZS5cbiAgICAgICAgICAgICAgICBpZiAobWF0Y2hlcyA9PT0gbnVsbCB8fCBtYXRjaGVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtYXRjaGVzKG5leHQsIG1hdGNoZWQubGVuZ3RoKSlcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgbWF0Y2hlZC5wdXNoKG5leHQpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIG1hdGNoZWQ7XG4gICAgfSkobmV4dEVsZW0sIC4uLnBvc3RGbnMpO1xuICAgIHJldHVybiBmdW5jdGlvbiAoc2VsZWN0b3IsIGZpbHRlclNlbGVjdG9yKSB7XG4gICAgICAgIC8vIE92ZXJyaWRlIGBtYXRjaGVzYCB2YXJpYWJsZSB3aXRoIHRoZSBuZXcgdGFyZ2V0LlxuICAgICAgICBtYXRjaGVzID1cbiAgICAgICAgICAgIHR5cGVvZiBzZWxlY3RvciA9PT0gJ3N0cmluZydcbiAgICAgICAgICAgICAgICA/IChlbGVtKSA9PiBzZWxlY3QuaXMoZWxlbSwgc2VsZWN0b3IsIHRoaXMub3B0aW9ucylcbiAgICAgICAgICAgICAgICA6IHNlbGVjdG9yXG4gICAgICAgICAgICAgICAgICAgID8gZ2V0RmlsdGVyRm4oc2VsZWN0b3IpXG4gICAgICAgICAgICAgICAgICAgIDogbnVsbDtcbiAgICAgICAgY29uc3QgcmV0ID0gaW5uZXJNYXRjaGVyLmNhbGwodGhpcywgZmlsdGVyU2VsZWN0b3IpO1xuICAgICAgICAvLyBTZXQgYG1hdGNoZXNgIHRvIGBudWxsYCwgc28gd2UgZG9uJ3Qgd2FzdGUgbWVtb3J5LlxuICAgICAgICBtYXRjaGVzID0gbnVsbDtcbiAgICAgICAgcmV0dXJuIHJldDtcbiAgICB9O1xufVxuZnVuY3Rpb24gX3JlbW92ZUR1cGxpY2F0ZXMoZWxlbXMpIHtcbiAgICByZXR1cm4gZWxlbXMubGVuZ3RoID4gMSA/IEFycmF5LmZyb20obmV3IFNldChlbGVtcykpIDogZWxlbXM7XG59XG4vKipcbiAqIEdldCB0aGUgcGFyZW50IG9mIGVhY2ggZWxlbWVudCBpbiB0aGUgY3VycmVudCBzZXQgb2YgbWF0Y2hlZCBlbGVtZW50cyxcbiAqIG9wdGlvbmFsbHkgZmlsdGVyZWQgYnkgYSBzZWxlY3Rvci5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnLnBlYXInKS5wYXJlbnQoKS5hdHRyKCdpZCcpO1xuICogLy89PiBmcnVpdHNcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzZWxlY3RvciAtIElmIHNwZWNpZmllZCBmaWx0ZXIgZm9yIHBhcmVudC5cbiAqIEByZXR1cm5zIFRoZSBwYXJlbnRzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9wYXJlbnQvfVxuICovXG5leHBvcnQgY29uc3QgcGFyZW50ID0gX3NpbmdsZU1hdGNoZXIoKHsgcGFyZW50IH0pID0+IChwYXJlbnQgJiYgIWlzRG9jdW1lbnQocGFyZW50KSA/IHBhcmVudCA6IG51bGwpLCBfcmVtb3ZlRHVwbGljYXRlcyk7XG4vKipcbiAqIEdldCBhIHNldCBvZiBwYXJlbnRzIGZpbHRlcmVkIGJ5IGBzZWxlY3RvcmAgb2YgZWFjaCBlbGVtZW50IGluIHRoZSBjdXJyZW50XG4gKiBzZXQgb2YgbWF0Y2ggZWxlbWVudHMuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJy5vcmFuZ2UnKS5wYXJlbnRzKCkubGVuZ3RoO1xuICogLy89PiAyXG4gKiAkKCcub3JhbmdlJykucGFyZW50cygnI2ZydWl0cycpLmxlbmd0aDtcbiAqIC8vPT4gMVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3IgcGFyZW50cy5cbiAqIEByZXR1cm5zIFRoZSBwYXJlbnRzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9wYXJlbnRzL31cbiAqL1xuZXhwb3J0IGNvbnN0IHBhcmVudHMgPSBfbWF0Y2hlcigoZWxlbSkgPT4ge1xuICAgIGNvbnN0IG1hdGNoZWQgPSBbXTtcbiAgICB3aGlsZSAoZWxlbS5wYXJlbnQgJiYgIWlzRG9jdW1lbnQoZWxlbS5wYXJlbnQpKSB7XG4gICAgICAgIG1hdGNoZWQucHVzaChlbGVtLnBhcmVudCk7XG4gICAgICAgIGVsZW0gPSBlbGVtLnBhcmVudDtcbiAgICB9XG4gICAgcmV0dXJuIG1hdGNoZWQ7XG59LCB1bmlxdWVTb3J0LCAoZWxlbXMpID0+IGVsZW1zLnJldmVyc2UoKSk7XG4vKipcbiAqIEdldCB0aGUgYW5jZXN0b3JzIG9mIGVhY2ggZWxlbWVudCBpbiB0aGUgY3VycmVudCBzZXQgb2YgbWF0Y2hlZCBlbGVtZW50cywgdXBcbiAqIHRvIGJ1dCBub3QgaW5jbHVkaW5nIHRoZSBlbGVtZW50IG1hdGNoZWQgYnkgdGhlIHNlbGVjdG9yLCBET00gbm9kZSwgb3JcbiAqIGNoZWVyaW8gb2JqZWN0LlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcub3JhbmdlJykucGFyZW50c1VudGlsKCcjZm9vZCcpLmxlbmd0aDtcbiAqIC8vPT4gMVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gU2VsZWN0b3IgZm9yIGVsZW1lbnQgdG8gc3RvcCBhdC5cbiAqIEBwYXJhbSBmaWx0ZXJTZWxlY3RvciAtIE9wdGlvbmFsIGZpbHRlciBmb3IgcGFyZW50cy5cbiAqIEByZXR1cm5zIFRoZSBwYXJlbnRzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9wYXJlbnRzVW50aWwvfVxuICovXG5leHBvcnQgY29uc3QgcGFyZW50c1VudGlsID0gX21hdGNoVW50aWwoKHsgcGFyZW50IH0pID0+IChwYXJlbnQgJiYgIWlzRG9jdW1lbnQocGFyZW50KSA/IHBhcmVudCA6IG51bGwpLCB1bmlxdWVTb3J0LCAoZWxlbXMpID0+IGVsZW1zLnJldmVyc2UoKSk7XG4vKipcbiAqIEZvciBlYWNoIGVsZW1lbnQgaW4gdGhlIHNldCwgZ2V0IHRoZSBmaXJzdCBlbGVtZW50IHRoYXQgbWF0Y2hlcyB0aGUgc2VsZWN0b3JcbiAqIGJ5IHRlc3RpbmcgdGhlIGVsZW1lbnQgaXRzZWxmIGFuZCB0cmF2ZXJzaW5nIHVwIHRocm91Z2ggaXRzIGFuY2VzdG9ycyBpbiB0aGVcbiAqIERPTSB0cmVlLlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcub3JhbmdlJykuY2xvc2VzdCgpO1xuICogLy89PiBbXVxuICpcbiAqICQoJy5vcmFuZ2UnKS5jbG9zZXN0KCcuYXBwbGUnKTtcbiAqIC8vID0+IFtdXG4gKlxuICogJCgnLm9yYW5nZScpLmNsb3Nlc3QoJ2xpJyk7XG4gKiAvLz0+IFs8bGkgY2xhc3M9XCJvcmFuZ2VcIj5PcmFuZ2U8L2xpPl1cbiAqXG4gKiAkKCcub3JhbmdlJykuY2xvc2VzdCgnI2ZydWl0cycpO1xuICogLy89PiBbPHVsIGlkPVwiZnJ1aXRzXCI+IC4uLiA8L3VsPl1cbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzZWxlY3RvciAtIFNlbGVjdG9yIGZvciB0aGUgZWxlbWVudCB0byBmaW5kLlxuICogQHJldHVybnMgVGhlIGNsb3Nlc3Qgbm9kZXMuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL2Nsb3Nlc3QvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2xvc2VzdChzZWxlY3Rvcikge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBzZXQgPSBbXTtcbiAgICBpZiAoIXNlbGVjdG9yKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9tYWtlKHNldCk7XG4gICAgfVxuICAgIGNvbnN0IHNlbGVjdE9wdHMgPSB7XG4gICAgICAgIHhtbE1vZGU6IHRoaXMub3B0aW9ucy54bWxNb2RlLFxuICAgICAgICByb290OiAoX2EgPSB0aGlzLl9yb290KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2FbMF0sXG4gICAgfTtcbiAgICBjb25zdCBzZWxlY3RGbiA9IHR5cGVvZiBzZWxlY3RvciA9PT0gJ3N0cmluZydcbiAgICAgICAgPyAoZWxlbSkgPT4gc2VsZWN0LmlzKGVsZW0sIHNlbGVjdG9yLCBzZWxlY3RPcHRzKVxuICAgICAgICA6IGdldEZpbHRlckZuKHNlbGVjdG9yKTtcbiAgICBkb21FYWNoKHRoaXMsIChlbGVtKSA9PiB7XG4gICAgICAgIGlmIChlbGVtICYmICFpc0RvY3VtZW50KGVsZW0pICYmICFpc1RhZyhlbGVtKSkge1xuICAgICAgICAgICAgZWxlbSA9IGVsZW0ucGFyZW50O1xuICAgICAgICB9XG4gICAgICAgIHdoaWxlIChlbGVtICYmIGlzVGFnKGVsZW0pKSB7XG4gICAgICAgICAgICBpZiAoc2VsZWN0Rm4oZWxlbSwgMCkpIHtcbiAgICAgICAgICAgICAgICAvLyBEbyBub3QgYWRkIGR1cGxpY2F0ZSBlbGVtZW50cyB0byB0aGUgc2V0XG4gICAgICAgICAgICAgICAgaWYgKCFzZXQuaW5jbHVkZXMoZWxlbSkpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0LnB1c2goZWxlbSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxlbSA9IGVsZW0ucGFyZW50O1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHRoaXMuX21ha2Uoc2V0KTtcbn1cbi8qKlxuICogR2V0cyB0aGUgbmV4dCBzaWJsaW5nIG9mIGVhY2ggc2VsZWN0ZWQgZWxlbWVudCwgb3B0aW9uYWxseSBmaWx0ZXJlZCBieSBhXG4gKiBzZWxlY3Rvci5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnLmFwcGxlJykubmV4dCgpLmhhc0NsYXNzKCdvcmFuZ2UnKTtcbiAqIC8vPT4gdHJ1ZVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3Igc2libGluZy5cbiAqIEByZXR1cm5zIFRoZSBuZXh0IG5vZGVzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9uZXh0L31cbiAqL1xuZXhwb3J0IGNvbnN0IG5leHQgPSBfc2luZ2xlTWF0Y2hlcigoZWxlbSkgPT4gbmV4dEVsZW1lbnRTaWJsaW5nKGVsZW0pKTtcbi8qKlxuICogR2V0cyBhbGwgdGhlIGZvbGxvd2luZyBzaWJsaW5ncyBvZiB0aGUgZWFjaCBzZWxlY3RlZCBlbGVtZW50LCBvcHRpb25hbGx5XG4gKiBmaWx0ZXJlZCBieSBhIHNlbGVjdG9yLlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcuYXBwbGUnKS5uZXh0QWxsKCk7XG4gKiAvLz0+IFs8bGkgY2xhc3M9XCJvcmFuZ2VcIj5PcmFuZ2U8L2xpPiwgPGxpIGNsYXNzPVwicGVhclwiPlBlYXI8L2xpPl1cbiAqICQoJy5hcHBsZScpLm5leHRBbGwoJy5vcmFuZ2UnKTtcbiAqIC8vPT4gWzxsaSBjbGFzcz1cIm9yYW5nZVwiPk9yYW5nZTwvbGk+XVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3Igc2libGluZ3MuXG4gKiBAcmV0dXJucyBUaGUgbmV4dCBub2Rlcy5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vbmV4dEFsbC99XG4gKi9cbmV4cG9ydCBjb25zdCBuZXh0QWxsID0gX21hdGNoZXIoKGVsZW0pID0+IHtcbiAgICBjb25zdCBtYXRjaGVkID0gW107XG4gICAgd2hpbGUgKGVsZW0ubmV4dCkge1xuICAgICAgICBlbGVtID0gZWxlbS5uZXh0O1xuICAgICAgICBpZiAoaXNUYWcoZWxlbSkpXG4gICAgICAgICAgICBtYXRjaGVkLnB1c2goZWxlbSk7XG4gICAgfVxuICAgIHJldHVybiBtYXRjaGVkO1xufSwgX3JlbW92ZUR1cGxpY2F0ZXMpO1xuLyoqXG4gKiBHZXRzIGFsbCB0aGUgZm9sbG93aW5nIHNpYmxpbmdzIHVwIHRvIGJ1dCBub3QgaW5jbHVkaW5nIHRoZSBlbGVtZW50IG1hdGNoZWRcbiAqIGJ5IHRoZSBzZWxlY3Rvciwgb3B0aW9uYWxseSBmaWx0ZXJlZCBieSBhbm90aGVyIHNlbGVjdG9yLlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcuYXBwbGUnKS5uZXh0VW50aWwoJy5wZWFyJyk7XG4gKiAvLz0+IFs8bGkgY2xhc3M9XCJvcmFuZ2VcIj5PcmFuZ2U8L2xpPl1cbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzZWxlY3RvciAtIFNlbGVjdG9yIGZvciBlbGVtZW50IHRvIHN0b3AgYXQuXG4gKiBAcGFyYW0gZmlsdGVyU2VsZWN0b3IgLSBJZiBzcGVjaWZpZWQgZmlsdGVyIGZvciBzaWJsaW5ncy5cbiAqIEByZXR1cm5zIFRoZSBuZXh0IG5vZGVzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9uZXh0VW50aWwvfVxuICovXG5leHBvcnQgY29uc3QgbmV4dFVudGlsID0gX21hdGNoVW50aWwoKGVsKSA9PiBuZXh0RWxlbWVudFNpYmxpbmcoZWwpLCBfcmVtb3ZlRHVwbGljYXRlcyk7XG4vKipcbiAqIEdldHMgdGhlIHByZXZpb3VzIHNpYmxpbmcgb2YgZWFjaCBzZWxlY3RlZCBlbGVtZW50IG9wdGlvbmFsbHkgZmlsdGVyZWQgYnkgYVxuICogc2VsZWN0b3IuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJy5vcmFuZ2UnKS5wcmV2KCkuaGFzQ2xhc3MoJ2FwcGxlJyk7XG4gKiAvLz0+IHRydWVcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzZWxlY3RvciAtIElmIHNwZWNpZmllZCBmaWx0ZXIgZm9yIHNpYmxpbmdzLlxuICogQHJldHVybnMgVGhlIHByZXZpb3VzIG5vZGVzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9wcmV2L31cbiAqL1xuZXhwb3J0IGNvbnN0IHByZXYgPSBfc2luZ2xlTWF0Y2hlcigoZWxlbSkgPT4gcHJldkVsZW1lbnRTaWJsaW5nKGVsZW0pKTtcbi8qKlxuICogR2V0cyBhbGwgdGhlIHByZWNlZGluZyBzaWJsaW5ncyBvZiBlYWNoIHNlbGVjdGVkIGVsZW1lbnQsIG9wdGlvbmFsbHkgZmlsdGVyZWRcbiAqIGJ5IGEgc2VsZWN0b3IuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJy5wZWFyJykucHJldkFsbCgpO1xuICogLy89PiBbPGxpIGNsYXNzPVwib3JhbmdlXCI+T3JhbmdlPC9saT4sIDxsaSBjbGFzcz1cImFwcGxlXCI+QXBwbGU8L2xpPl1cbiAqXG4gKiAkKCcucGVhcicpLnByZXZBbGwoJy5vcmFuZ2UnKTtcbiAqIC8vPT4gWzxsaSBjbGFzcz1cIm9yYW5nZVwiPk9yYW5nZTwvbGk+XVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3Igc2libGluZ3MuXG4gKiBAcmV0dXJucyBUaGUgcHJldmlvdXMgbm9kZXMuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL3ByZXZBbGwvfVxuICovXG5leHBvcnQgY29uc3QgcHJldkFsbCA9IF9tYXRjaGVyKChlbGVtKSA9PiB7XG4gICAgY29uc3QgbWF0Y2hlZCA9IFtdO1xuICAgIHdoaWxlIChlbGVtLnByZXYpIHtcbiAgICAgICAgZWxlbSA9IGVsZW0ucHJldjtcbiAgICAgICAgaWYgKGlzVGFnKGVsZW0pKVxuICAgICAgICAgICAgbWF0Y2hlZC5wdXNoKGVsZW0pO1xuICAgIH1cbiAgICByZXR1cm4gbWF0Y2hlZDtcbn0sIF9yZW1vdmVEdXBsaWNhdGVzKTtcbi8qKlxuICogR2V0cyBhbGwgdGhlIHByZWNlZGluZyBzaWJsaW5ncyB1cCB0byBidXQgbm90IGluY2x1ZGluZyB0aGUgZWxlbWVudCBtYXRjaGVkXG4gKiBieSB0aGUgc2VsZWN0b3IsIG9wdGlvbmFsbHkgZmlsdGVyZWQgYnkgYW5vdGhlciBzZWxlY3Rvci5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnLnBlYXInKS5wcmV2VW50aWwoJy5hcHBsZScpO1xuICogLy89PiBbPGxpIGNsYXNzPVwib3JhbmdlXCI+T3JhbmdlPC9saT5dXG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0gc2VsZWN0b3IgLSBTZWxlY3RvciBmb3IgZWxlbWVudCB0byBzdG9wIGF0LlxuICogQHBhcmFtIGZpbHRlclNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3Igc2libGluZ3MuXG4gKiBAcmV0dXJucyBUaGUgcHJldmlvdXMgbm9kZXMuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL3ByZXZVbnRpbC99XG4gKi9cbmV4cG9ydCBjb25zdCBwcmV2VW50aWwgPSBfbWF0Y2hVbnRpbCgoZWwpID0+IHByZXZFbGVtZW50U2libGluZyhlbCksIF9yZW1vdmVEdXBsaWNhdGVzKTtcbi8qKlxuICogR2V0IHRoZSBzaWJsaW5ncyBvZiBlYWNoIGVsZW1lbnQgKGV4Y2x1ZGluZyB0aGUgZWxlbWVudCkgaW4gdGhlIHNldCBvZlxuICogbWF0Y2hlZCBlbGVtZW50cywgb3B0aW9uYWxseSBmaWx0ZXJlZCBieSBhIHNlbGVjdG9yLlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcucGVhcicpLnNpYmxpbmdzKCkubGVuZ3RoO1xuICogLy89PiAyXG4gKlxuICogJCgnLnBlYXInKS5zaWJsaW5ncygnLm9yYW5nZScpLmxlbmd0aDtcbiAqIC8vPT4gMVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gSWYgc3BlY2lmaWVkIGZpbHRlciBmb3Igc2libGluZ3MuXG4gKiBAcmV0dXJucyBUaGUgc2libGluZ3MuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL3NpYmxpbmdzL31cbiAqL1xuZXhwb3J0IGNvbnN0IHNpYmxpbmdzID0gX21hdGNoZXIoKGVsZW0pID0+IGdldFNpYmxpbmdzKGVsZW0pLmZpbHRlcigoZWwpID0+IGlzVGFnKGVsKSAmJiBlbCAhPT0gZWxlbSksIHVuaXF1ZVNvcnQpO1xuLyoqXG4gKiBHZXRzIHRoZSBlbGVtZW50IGNoaWxkcmVuIG9mIGVhY2ggZWxlbWVudCBpbiB0aGUgc2V0IG9mIG1hdGNoZWQgZWxlbWVudHMuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJyNmcnVpdHMnKS5jaGlsZHJlbigpLmxlbmd0aDtcbiAqIC8vPT4gM1xuICpcbiAqICQoJyNmcnVpdHMnKS5jaGlsZHJlbignLnBlYXInKS50ZXh0KCk7XG4gKiAvLz0+IFBlYXJcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzZWxlY3RvciAtIElmIHNwZWNpZmllZCBmaWx0ZXIgZm9yIGNoaWxkcmVuLlxuICogQHJldHVybnMgVGhlIGNoaWxkcmVuLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9jaGlsZHJlbi99XG4gKi9cbmV4cG9ydCBjb25zdCBjaGlsZHJlbiA9IF9tYXRjaGVyKChlbGVtKSA9PiBnZXRDaGlsZHJlbihlbGVtKS5maWx0ZXIoaXNUYWcpLCBfcmVtb3ZlRHVwbGljYXRlcyk7XG4vKipcbiAqIEdldHMgdGhlIGNoaWxkcmVuIG9mIGVhY2ggZWxlbWVudCBpbiB0aGUgc2V0IG9mIG1hdGNoZWQgZWxlbWVudHMsIGluY2x1ZGluZ1xuICogdGV4dCBhbmQgY29tbWVudCBub2Rlcy5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnI2ZydWl0cycpLmNvbnRlbnRzKCkubGVuZ3RoO1xuICogLy89PiAzXG4gKiBgYGBcbiAqXG4gKiBAcmV0dXJucyBUaGUgY2hpbGRyZW4uXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL2NvbnRlbnRzL31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbnRlbnRzKCkge1xuICAgIGNvbnN0IGVsZW1zID0gdGhpcy50b0FycmF5KCkucmVkdWNlKChuZXdFbGVtcywgZWxlbSkgPT4gaGFzQ2hpbGRyZW4oZWxlbSkgPyBuZXdFbGVtcy5jb25jYXQoZWxlbS5jaGlsZHJlbikgOiBuZXdFbGVtcywgW10pO1xuICAgIHJldHVybiB0aGlzLl9tYWtlKGVsZW1zKTtcbn1cbi8qKlxuICogSXRlcmF0ZXMgb3ZlciBhIGNoZWVyaW8gb2JqZWN0LCBleGVjdXRpbmcgYSBmdW5jdGlvbiBmb3IgZWFjaCBtYXRjaGVkXG4gKiBlbGVtZW50LiBXaGVuIHRoZSBjYWxsYmFjayBpcyBmaXJlZCwgdGhlIGZ1bmN0aW9uIGlzIGZpcmVkIGluIHRoZSBjb250ZXh0IG9mXG4gKiB0aGUgRE9NIGVsZW1lbnQsIHNvIGB0aGlzYCByZWZlcnMgdG8gdGhlIGN1cnJlbnQgZWxlbWVudCwgd2hpY2ggaXMgZXF1aXZhbGVudFxuICogdG8gdGhlIGZ1bmN0aW9uIHBhcmFtZXRlciBgZWxlbWVudGAuIFRvIGJyZWFrIG91dCBvZiB0aGUgYGVhY2hgIGxvb3AgZWFybHksXG4gKiByZXR1cm4gd2l0aCBgZmFsc2VgLlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiBjb25zdCBmcnVpdHMgPSBbXTtcbiAqXG4gKiAkKCdsaScpLmVhY2goZnVuY3Rpb24gKGksIGVsZW0pIHtcbiAqICAgZnJ1aXRzW2ldID0gJCh0aGlzKS50ZXh0KCk7XG4gKiB9KTtcbiAqXG4gKiBmcnVpdHMuam9pbignLCAnKTtcbiAqIC8vPT4gQXBwbGUsIE9yYW5nZSwgUGVhclxuICogYGBgXG4gKlxuICogQHBhcmFtIGZuIC0gRnVuY3Rpb24gdG8gZXhlY3V0ZS5cbiAqIEByZXR1cm5zIFRoZSBpbnN0YW5jZSBpdHNlbGYsIHVzZWZ1bCBmb3IgY2hhaW5pbmcuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2FwaS5qcXVlcnkuY29tL2VhY2gvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZWFjaChmbikge1xuICAgIGxldCBpID0gMDtcbiAgICBjb25zdCBsZW4gPSB0aGlzLmxlbmd0aDtcbiAgICB3aGlsZSAoaSA8IGxlbiAmJiBmbi5jYWxsKHRoaXNbaV0sIGksIHRoaXNbaV0pICE9PSBmYWxzZSlcbiAgICAgICAgKytpO1xuICAgIHJldHVybiB0aGlzO1xufVxuLyoqXG4gKiBQYXNzIGVhY2ggZWxlbWVudCBpbiB0aGUgY3VycmVudCBtYXRjaGVkIHNldCB0aHJvdWdoIGEgZnVuY3Rpb24sIHByb2R1Y2luZyBhXG4gKiBuZXcgQ2hlZXJpbyBvYmplY3QgY29udGFpbmluZyB0aGUgcmV0dXJuIHZhbHVlcy4gVGhlIGZ1bmN0aW9uIGNhbiByZXR1cm4gYW5cbiAqIGluZGl2aWR1YWwgZGF0YSBpdGVtIG9yIGFuIGFycmF5IG9mIGRhdGEgaXRlbXMgdG8gYmUgaW5zZXJ0ZWQgaW50byB0aGVcbiAqIHJlc3VsdGluZyBzZXQuIElmIGFuIGFycmF5IGlzIHJldHVybmVkLCB0aGUgZWxlbWVudHMgaW5zaWRlIHRoZSBhcnJheSBhcmVcbiAqIGluc2VydGVkIGludG8gdGhlIHNldC4gSWYgdGhlIGZ1bmN0aW9uIHJldHVybnMgbnVsbCBvciB1bmRlZmluZWQsIG5vIGVsZW1lbnRcbiAqIHdpbGwgYmUgaW5zZXJ0ZWQuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJ2xpJylcbiAqICAgLm1hcChmdW5jdGlvbiAoaSwgZWwpIHtcbiAqICAgICAvLyB0aGlzID09PSBlbFxuICogICAgIHJldHVybiAkKHRoaXMpLnRleHQoKTtcbiAqICAgfSlcbiAqICAgLnRvQXJyYXkoKVxuICogICAuam9pbignICcpO1xuICogLy89PiBcImFwcGxlIG9yYW5nZSBwZWFyXCJcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBmbiAtIEZ1bmN0aW9uIHRvIGV4ZWN1dGUuXG4gKiBAcmV0dXJucyBUaGUgbWFwcGVkIGVsZW1lbnRzLCB3cmFwcGVkIGluIGEgQ2hlZXJpbyBjb2xsZWN0aW9uLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9tYXAvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbWFwKGZuKSB7XG4gICAgbGV0IGVsZW1zID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGVsID0gdGhpc1tpXTtcbiAgICAgICAgY29uc3QgdmFsID0gZm4uY2FsbChlbCwgaSwgZWwpO1xuICAgICAgICBpZiAodmFsICE9IG51bGwpIHtcbiAgICAgICAgICAgIGVsZW1zID0gZWxlbXMuY29uY2F0KHZhbCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRoaXMuX21ha2UoZWxlbXMpO1xufVxuLyoqXG4gKiBDcmVhdGVzIGEgZnVuY3Rpb24gdG8gdGVzdCBpZiBhIGZpbHRlciBpcyBtYXRjaGVkLlxuICpcbiAqIEBwYXJhbSBtYXRjaCAtIEEgZmlsdGVyLlxuICogQHJldHVybnMgQSBmdW5jdGlvbiB0aGF0IGRldGVybWluZXMgaWYgYSBmaWx0ZXIgaGFzIGJlZW4gbWF0Y2hlZC5cbiAqL1xuZnVuY3Rpb24gZ2V0RmlsdGVyRm4obWF0Y2gpIHtcbiAgICBpZiAodHlwZW9mIG1hdGNoID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiAoZWwsIGkpID0+IG1hdGNoLmNhbGwoZWwsIGksIGVsKTtcbiAgICB9XG4gICAgaWYgKGlzQ2hlZXJpbyhtYXRjaCkpIHtcbiAgICAgICAgcmV0dXJuIChlbCkgPT4gQXJyYXkucHJvdG90eXBlLmluY2x1ZGVzLmNhbGwobWF0Y2gsIGVsKTtcbiAgICB9XG4gICAgcmV0dXJuIGZ1bmN0aW9uIChlbCkge1xuICAgICAgICByZXR1cm4gbWF0Y2ggPT09IGVsO1xuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gZmlsdGVyKG1hdGNoKSB7XG4gICAgdmFyIF9hO1xuICAgIHJldHVybiB0aGlzLl9tYWtlKGZpbHRlckFycmF5KHRoaXMudG9BcnJheSgpLCBtYXRjaCwgdGhpcy5vcHRpb25zLnhtbE1vZGUsIChfYSA9IHRoaXMuX3Jvb3QpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYVswXSkpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGZpbHRlckFycmF5KG5vZGVzLCBtYXRjaCwgeG1sTW9kZSwgcm9vdCkge1xuICAgIHJldHVybiB0eXBlb2YgbWF0Y2ggPT09ICdzdHJpbmcnXG4gICAgICAgID8gc2VsZWN0LmZpbHRlcihtYXRjaCwgbm9kZXMsIHsgeG1sTW9kZSwgcm9vdCB9KVxuICAgICAgICA6IG5vZGVzLmZpbHRlcihnZXRGaWx0ZXJGbihtYXRjaCkpO1xufVxuLyoqXG4gKiBDaGVja3MgdGhlIGN1cnJlbnQgbGlzdCBvZiBlbGVtZW50cyBhbmQgcmV0dXJucyBgdHJ1ZWAgaWYgX2FueV8gb2YgdGhlXG4gKiBlbGVtZW50cyBtYXRjaCB0aGUgc2VsZWN0b3IuIElmIHVzaW5nIGFuIGVsZW1lbnQgb3IgQ2hlZXJpbyBzZWxlY3Rpb24sXG4gKiByZXR1cm5zIGB0cnVlYCBpZiBfYW55XyBvZiB0aGUgZWxlbWVudHMgbWF0Y2guIElmIHVzaW5nIGEgcHJlZGljYXRlIGZ1bmN0aW9uLFxuICogdGhlIGZ1bmN0aW9uIGlzIGV4ZWN1dGVkIGluIHRoZSBjb250ZXh0IG9mIHRoZSBzZWxlY3RlZCBlbGVtZW50LCBzbyBgdGhpc2BcbiAqIHJlZmVycyB0byB0aGUgY3VycmVudCBlbGVtZW50LlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAcGFyYW0gc2VsZWN0b3IgLSBTZWxlY3RvciBmb3IgdGhlIHNlbGVjdGlvbi5cbiAqIEByZXR1cm5zIFdoZXRoZXIgb3Igbm90IHRoZSBzZWxlY3RvciBtYXRjaGVzIGFuIGVsZW1lbnQgb2YgdGhlIGluc3RhbmNlLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9pcy99XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpcyhzZWxlY3Rvcikge1xuICAgIGNvbnN0IG5vZGVzID0gdGhpcy50b0FycmF5KCk7XG4gICAgcmV0dXJuIHR5cGVvZiBzZWxlY3RvciA9PT0gJ3N0cmluZydcbiAgICAgICAgPyBzZWxlY3Quc29tZShub2Rlcy5maWx0ZXIoaXNUYWcpLCBzZWxlY3RvciwgdGhpcy5vcHRpb25zKVxuICAgICAgICA6IHNlbGVjdG9yXG4gICAgICAgICAgICA/IG5vZGVzLnNvbWUoZ2V0RmlsdGVyRm4oc2VsZWN0b3IpKVxuICAgICAgICAgICAgOiBmYWxzZTtcbn1cbi8qKlxuICogUmVtb3ZlIGVsZW1lbnRzIGZyb20gdGhlIHNldCBvZiBtYXRjaGVkIGVsZW1lbnRzLiBHaXZlbiBhIENoZWVyaW8gb2JqZWN0IHRoYXRcbiAqIHJlcHJlc2VudHMgYSBzZXQgb2YgRE9NIGVsZW1lbnRzLCB0aGUgYC5ub3QoKWAgbWV0aG9kIGNvbnN0cnVjdHMgYSBuZXdcbiAqIENoZWVyaW8gb2JqZWN0IGZyb20gYSBzdWJzZXQgb2YgdGhlIG1hdGNoaW5nIGVsZW1lbnRzLiBUaGUgc3VwcGxpZWQgc2VsZWN0b3JcbiAqIGlzIHRlc3RlZCBhZ2FpbnN0IGVhY2ggZWxlbWVudDsgdGhlIGVsZW1lbnRzIHRoYXQgZG9uJ3QgbWF0Y2ggdGhlIHNlbGVjdG9yXG4gKiB3aWxsIGJlIGluY2x1ZGVkIGluIHRoZSByZXN1bHQuXG4gKlxuICogVGhlIGAubm90KClgIG1ldGhvZCBjYW4gdGFrZSBhIGZ1bmN0aW9uIGFzIGl0cyBhcmd1bWVudCBpbiB0aGUgc2FtZSB3YXkgdGhhdFxuICogYC5maWx0ZXIoKWAgZG9lcy4gRWxlbWVudHMgZm9yIHdoaWNoIHRoZSBmdW5jdGlvbiByZXR1cm5zIGB0cnVlYCBhcmUgZXhjbHVkZWRcbiAqIGZyb20gdGhlIGZpbHRlcmVkIHNldDsgYWxsIG90aGVyIGVsZW1lbnRzIGFyZSBpbmNsdWRlZC5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGUgPGNhcHRpb24+U2VsZWN0b3I8L2NhcHRpb24+XG4gKlxuICogYGBganNcbiAqICQoJ2xpJykubm90KCcuYXBwbGUnKS5sZW5ndGg7XG4gKiAvLz0+IDJcbiAqIGBgYFxuICpcbiAqIEBleGFtcGxlIDxjYXB0aW9uPkZ1bmN0aW9uPC9jYXB0aW9uPlxuICpcbiAqIGBgYGpzXG4gKiAkKCdsaScpLm5vdChmdW5jdGlvbiAoaSwgZWwpIHtcbiAqICAgLy8gdGhpcyA9PT0gZWxcbiAqICAgcmV0dXJuICQodGhpcykuYXR0cignY2xhc3MnKSA9PT0gJ29yYW5nZSc7XG4gKiB9KS5sZW5ndGg7IC8vPT4gMlxuICogYGBgXG4gKlxuICogQHBhcmFtIG1hdGNoIC0gVmFsdWUgdG8gbG9vayBmb3IsIGZvbGxvd2luZyB0aGUgcnVsZXMgYWJvdmUuXG4gKiBAcmV0dXJucyBUaGUgZmlsdGVyZWQgY29sbGVjdGlvbi5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vbm90L31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG5vdChtYXRjaCkge1xuICAgIGxldCBub2RlcyA9IHRoaXMudG9BcnJheSgpO1xuICAgIGlmICh0eXBlb2YgbWF0Y2ggPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoZXMgPSBuZXcgU2V0KHNlbGVjdC5maWx0ZXIobWF0Y2gsIG5vZGVzLCB0aGlzLm9wdGlvbnMpKTtcbiAgICAgICAgbm9kZXMgPSBub2Rlcy5maWx0ZXIoKGVsKSA9PiAhbWF0Y2hlcy5oYXMoZWwpKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGNvbnN0IGZpbHRlckZuID0gZ2V0RmlsdGVyRm4obWF0Y2gpO1xuICAgICAgICBub2RlcyA9IG5vZGVzLmZpbHRlcigoZWwsIGkpID0+ICFmaWx0ZXJGbihlbCwgaSkpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fbWFrZShub2Rlcyk7XG59XG4vKipcbiAqIEZpbHRlcnMgdGhlIHNldCBvZiBtYXRjaGVkIGVsZW1lbnRzIHRvIG9ubHkgdGhvc2Ugd2hpY2ggaGF2ZSB0aGUgZ2l2ZW4gRE9NXG4gKiBlbGVtZW50IGFzIGEgZGVzY2VuZGFudCBvciB3aGljaCBoYXZlIGEgZGVzY2VuZGFudCB0aGF0IG1hdGNoZXMgdGhlIGdpdmVuXG4gKiBzZWxlY3Rvci4gRXF1aXZhbGVudCB0byBgLmZpbHRlcignOmhhcyhzZWxlY3RvciknKWAuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlIDxjYXB0aW9uPlNlbGVjdG9yPC9jYXB0aW9uPlxuICpcbiAqIGBgYGpzXG4gKiAkKCd1bCcpLmhhcygnLnBlYXInKS5hdHRyKCdpZCcpO1xuICogLy89PiBmcnVpdHNcbiAqIGBgYFxuICpcbiAqIEBleGFtcGxlIDxjYXB0aW9uPkVsZW1lbnQ8L2NhcHRpb24+XG4gKlxuICogYGBganNcbiAqICQoJ3VsJykuaGFzKCQoJy5wZWFyJylbMF0pLmF0dHIoJ2lkJyk7XG4gKiAvLz0+IGZydWl0c1xuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yT3JIYXlzdGFjayAtIEVsZW1lbnQgdG8gbG9vayBmb3IuXG4gKiBAcmV0dXJucyBUaGUgZmlsdGVyZWQgY29sbGVjdGlvbi5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vaGFzL31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcyhzZWxlY3Rvck9ySGF5c3RhY2spIHtcbiAgICByZXR1cm4gdGhpcy5maWx0ZXIodHlwZW9mIHNlbGVjdG9yT3JIYXlzdGFjayA9PT0gJ3N0cmluZydcbiAgICAgICAgPyAvLyBVc2luZyB0aGUgYDpoYXNgIHNlbGVjdG9yIGhlcmUgc2hvcnQtY2lyY3VpdHMgc2VhcmNoZXMuXG4gICAgICAgICAgICBgOmhhcygke3NlbGVjdG9yT3JIYXlzdGFja30pYFxuICAgICAgICA6IChfLCBlbCkgPT4gdGhpcy5fbWFrZShlbCkuZmluZChzZWxlY3Rvck9ySGF5c3RhY2spLmxlbmd0aCA+IDApO1xufVxuLyoqXG4gKiBXaWxsIHNlbGVjdCB0aGUgZmlyc3QgZWxlbWVudCBvZiBhIGNoZWVyaW8gb2JqZWN0LlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzaW5nXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCcjZnJ1aXRzJykuY2hpbGRyZW4oKS5maXJzdCgpLnRleHQoKTtcbiAqIC8vPT4gQXBwbGVcbiAqIGBgYFxuICpcbiAqIEByZXR1cm5zIFRoZSBmaXJzdCBlbGVtZW50LlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9maXJzdC99XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaXJzdCgpIHtcbiAgICByZXR1cm4gdGhpcy5sZW5ndGggPiAxID8gdGhpcy5fbWFrZSh0aGlzWzBdKSA6IHRoaXM7XG59XG4vKipcbiAqIFdpbGwgc2VsZWN0IHRoZSBsYXN0IGVsZW1lbnQgb2YgYSBjaGVlcmlvIG9iamVjdC5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnI2ZydWl0cycpLmNoaWxkcmVuKCkubGFzdCgpLnRleHQoKTtcbiAqIC8vPT4gUGVhclxuICogYGBgXG4gKlxuICogQHJldHVybnMgVGhlIGxhc3QgZWxlbWVudC5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vbGFzdC99XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsYXN0KCkge1xuICAgIHJldHVybiB0aGlzLmxlbmd0aCA+IDAgPyB0aGlzLl9tYWtlKHRoaXNbdGhpcy5sZW5ndGggLSAxXSkgOiB0aGlzO1xufVxuLyoqXG4gKiBSZWR1Y2UgdGhlIHNldCBvZiBtYXRjaGVkIGVsZW1lbnRzIHRvIHRoZSBvbmUgYXQgdGhlIHNwZWNpZmllZCBpbmRleC4gVXNlXG4gKiBgLmVxKC1pKWAgdG8gY291bnQgYmFja3dhcmRzIGZyb20gdGhlIGxhc3Qgc2VsZWN0ZWQgZWxlbWVudC5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnbGknKS5lcSgwKS50ZXh0KCk7XG4gKiAvLz0+IEFwcGxlXG4gKlxuICogJCgnbGknKS5lcSgtMSkudGV4dCgpO1xuICogLy89PiBQZWFyXG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0gaSAtIEluZGV4IG9mIHRoZSBlbGVtZW50IHRvIHNlbGVjdC5cbiAqIEByZXR1cm5zIFRoZSBlbGVtZW50IGF0IHRoZSBgaWB0aCBwb3NpdGlvbi5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vZXEvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZXEoaSkge1xuICAgIHZhciBfYTtcbiAgICBpID0gK2k7XG4gICAgLy8gVXNlIHRoZSBmaXJzdCBpZGVudGl0eSBvcHRpbWl6YXRpb24gaWYgcG9zc2libGVcbiAgICBpZiAoaSA9PT0gMCAmJiB0aGlzLmxlbmd0aCA8PSAxKVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICBpZiAoaSA8IDApXG4gICAgICAgIGkgPSB0aGlzLmxlbmd0aCArIGk7XG4gICAgcmV0dXJuIHRoaXMuX21ha2UoKF9hID0gdGhpc1tpXSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogW10pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldChpKSB7XG4gICAgaWYgKGkgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gdGhpcy50b0FycmF5KCk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzW2kgPCAwID8gdGhpcy5sZW5ndGggKyBpIDogaV07XG59XG4vKipcbiAqIFJldHJpZXZlIGFsbCB0aGUgRE9NIGVsZW1lbnRzIGNvbnRhaW5lZCBpbiB0aGUgalF1ZXJ5IHNldCBhcyBhbiBhcnJheS5cbiAqXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYGpzXG4gKiAkKCdsaScpLnRvQXJyYXkoKTtcbiAqIC8vPT4gWyB7Li4ufSwgey4uLn0sIHsuLi59IF1cbiAqIGBgYFxuICpcbiAqIEByZXR1cm5zIFRoZSBjb250YWluZWQgaXRlbXMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b0FycmF5KCkge1xuICAgIHJldHVybiBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbCh0aGlzKTtcbn1cbi8qKlxuICogU2VhcmNoIGZvciBhIGdpdmVuIGVsZW1lbnQgZnJvbSBhbW9uZyB0aGUgbWF0Y2hlZCBlbGVtZW50cy5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnLnBlYXInKS5pbmRleCgpO1xuICogLy89PiAyICQoJy5vcmFuZ2UnKS5pbmRleCgnbGknKTtcbiAqIC8vPT4gMVxuICogJCgnLmFwcGxlJykuaW5kZXgoJCgnI2ZydWl0LCBsaScpKTtcbiAqIC8vPT4gMVxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yT3JOZWVkbGUgLSBFbGVtZW50IHRvIGxvb2sgZm9yLlxuICogQHJldHVybnMgVGhlIGluZGV4IG9mIHRoZSBlbGVtZW50LlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9pbmRleC99XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmRleChzZWxlY3Rvck9yTmVlZGxlKSB7XG4gICAgbGV0ICRoYXlzdGFjaztcbiAgICBsZXQgbmVlZGxlO1xuICAgIGlmIChzZWxlY3Rvck9yTmVlZGxlID09IG51bGwpIHtcbiAgICAgICAgJGhheXN0YWNrID0gdGhpcy5wYXJlbnQoKS5jaGlsZHJlbigpO1xuICAgICAgICBuZWVkbGUgPSB0aGlzWzBdO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2Ygc2VsZWN0b3JPck5lZWRsZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgJGhheXN0YWNrID0gdGhpcy5fbWFrZShzZWxlY3Rvck9yTmVlZGxlKTtcbiAgICAgICAgbmVlZGxlID0gdGhpc1swXTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdGhpcy1hbGlhcywgdW5pY29ybi9uby10aGlzLWFzc2lnbm1lbnRcbiAgICAgICAgJGhheXN0YWNrID0gdGhpcztcbiAgICAgICAgbmVlZGxlID0gaXNDaGVlcmlvKHNlbGVjdG9yT3JOZWVkbGUpXG4gICAgICAgICAgICA/IHNlbGVjdG9yT3JOZWVkbGVbMF1cbiAgICAgICAgICAgIDogc2VsZWN0b3JPck5lZWRsZTtcbiAgICB9XG4gICAgcmV0dXJuIEFycmF5LnByb3RvdHlwZS5pbmRleE9mLmNhbGwoJGhheXN0YWNrLCBuZWVkbGUpO1xufVxuLyoqXG4gKiBHZXRzIHRoZSBlbGVtZW50cyBtYXRjaGluZyB0aGUgc3BlY2lmaWVkIHJhbmdlICgwLWJhc2VkIHBvc2l0aW9uKS5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnbGknKS5zbGljZSgxKS5lcSgwKS50ZXh0KCk7XG4gKiAvLz0+ICdPcmFuZ2UnXG4gKlxuICogJCgnbGknKS5zbGljZSgxLCAyKS5sZW5ndGg7XG4gKiAvLz0+IDFcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBzdGFydCAtIEEgcG9zaXRpb24gYXQgd2hpY2ggdGhlIGVsZW1lbnRzIGJlZ2luIHRvIGJlIHNlbGVjdGVkLiBJZlxuICogICBuZWdhdGl2ZSwgaXQgaW5kaWNhdGVzIGFuIG9mZnNldCBmcm9tIHRoZSBlbmQgb2YgdGhlIHNldC5cbiAqIEBwYXJhbSBlbmQgLSBBIHBvc2l0aW9uIGF0IHdoaWNoIHRoZSBlbGVtZW50cyBzdG9wIGJlaW5nIHNlbGVjdGVkLiBJZlxuICogICBuZWdhdGl2ZSwgaXQgaW5kaWNhdGVzIGFuIG9mZnNldCBmcm9tIHRoZSBlbmQgb2YgdGhlIHNldC4gSWYgb21pdHRlZCwgdGhlXG4gKiAgIHJhbmdlIGNvbnRpbnVlcyB1bnRpbCB0aGUgZW5kIG9mIHRoZSBzZXQuXG4gKiBAcmV0dXJucyBUaGUgZWxlbWVudHMgbWF0Y2hpbmcgdGhlIHNwZWNpZmllZCByYW5nZS5cbiAqIEBzZWUge0BsaW5rIGh0dHBzOi8vYXBpLmpxdWVyeS5jb20vc2xpY2UvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gc2xpY2Uoc3RhcnQsIGVuZCkge1xuICAgIHJldHVybiB0aGlzLl9tYWtlKEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKHRoaXMsIHN0YXJ0LCBlbmQpKTtcbn1cbi8qKlxuICogRW5kIHRoZSBtb3N0IHJlY2VudCBmaWx0ZXJpbmcgb3BlcmF0aW9uIGluIHRoZSBjdXJyZW50IGNoYWluIGFuZCByZXR1cm4gdGhlXG4gKiBzZXQgb2YgbWF0Y2hlZCBlbGVtZW50cyB0byBpdHMgcHJldmlvdXMgc3RhdGUuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJ2xpJykuZXEoMCkuZW5kKCkubGVuZ3RoO1xuICogLy89PiAzXG4gKiBgYGBcbiAqXG4gKiBAcmV0dXJucyBUaGUgcHJldmlvdXMgc3RhdGUgb2YgdGhlIHNldCBvZiBtYXRjaGVkIGVsZW1lbnRzLlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9lbmQvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZW5kKCkge1xuICAgIHZhciBfYTtcbiAgICByZXR1cm4gKF9hID0gdGhpcy5wcmV2T2JqZWN0KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiB0aGlzLl9tYWtlKFtdKTtcbn1cbi8qKlxuICogQWRkIGVsZW1lbnRzIHRvIHRoZSBzZXQgb2YgbWF0Y2hlZCBlbGVtZW50cy5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2luZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogJCgnLmFwcGxlJykuYWRkKCcub3JhbmdlJykubGVuZ3RoO1xuICogLy89PiAyXG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0gb3RoZXIgLSBFbGVtZW50cyB0byBhZGQuXG4gKiBAcGFyYW0gY29udGV4dCAtIE9wdGlvbmFsbHkgdGhlIGNvbnRleHQgb2YgdGhlIG5ldyBzZWxlY3Rpb24uXG4gKiBAcmV0dXJucyBUaGUgY29tYmluZWQgc2V0LlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9hZGQvfVxuICovXG5leHBvcnQgZnVuY3Rpb24gYWRkKG90aGVyLCBjb250ZXh0KSB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gdGhpcy5fbWFrZShvdGhlciwgY29udGV4dCk7XG4gICAgY29uc3QgY29udGVudHMgPSB1bmlxdWVTb3J0KFsuLi50aGlzLmdldCgpLCAuLi5zZWxlY3Rpb24uZ2V0KCldKTtcbiAgICByZXR1cm4gdGhpcy5fbWFrZShjb250ZW50cyk7XG59XG4vKipcbiAqIEFkZCB0aGUgcHJldmlvdXMgc2V0IG9mIGVsZW1lbnRzIG9uIHRoZSBzdGFjayB0byB0aGUgY3VycmVudCBzZXQsIG9wdGlvbmFsbHlcbiAqIGZpbHRlcmVkIGJ5IGEgc2VsZWN0b3IuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqICQoJ2xpJykuZXEoMCkuYWRkQmFjaygnLm9yYW5nZScpLmxlbmd0aDtcbiAqIC8vPT4gMlxuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbGVjdG9yIC0gU2VsZWN0b3IgZm9yIHRoZSBlbGVtZW50cyB0byBhZGQuXG4gKiBAcmV0dXJucyBUaGUgY29tYmluZWQgc2V0LlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9hcGkuanF1ZXJ5LmNvbS9hZGRCYWNrL31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGFkZEJhY2soc2VsZWN0b3IpIHtcbiAgICByZXR1cm4gdGhpcy5wcmV2T2JqZWN0XG4gICAgICAgID8gdGhpcy5hZGQoc2VsZWN0b3IgPyB0aGlzLnByZXZPYmplY3QuZmlsdGVyKHNlbGVjdG9yKSA6IHRoaXMucHJldk9iamVjdClcbiAgICAgICAgOiB0aGlzO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhdmVyc2luZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/cheerio.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/cheerio.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cheerio: () => (/* binding */ Cheerio)\n/* harmony export */ });\n/* harmony import */ var _api_attributes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/attributes.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js\");\n/* harmony import */ var _api_traversing_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api/traversing.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js\");\n/* harmony import */ var _api_manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api/manipulation.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js\");\n/* harmony import */ var _api_css_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api/css.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/css.js\");\n/* harmony import */ var _api_forms_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api/forms.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/forms.js\");\n/* harmony import */ var _api_extract_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./api/extract.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/extract.js\");\n\n\n\n\n\n\n/**\n * The cheerio class is the central class of the library. It wraps a set of\n * elements and provides an API for traversing, modifying, and interacting with\n * the set.\n *\n * Loading a document will return the Cheerio class bound to the root element of\n * the document. The class will be instantiated when querying the document (when\n * calling `$('selector')`).\n *\n * @example This is the HTML markup we will be using in all of the API examples:\n *\n * ```html\n * <ul id=\"fruits\">\n *   <li class=\"apple\">Apple</li>\n *   <li class=\"orange\">Orange</li>\n *   <li class=\"pear\">Pear</li>\n * </ul>\n * ```\n */\nclass Cheerio {\n    /**\n     * Instance of cheerio. Methods are specified in the modules. Usage of this\n     * constructor is not recommended. Please use `$.load` instead.\n     *\n     * @private\n     * @param elements - The new selection.\n     * @param root - Sets the root node.\n     * @param options - Options for the instance.\n     */\n    constructor(elements, root, options) {\n        this.length = 0;\n        this.options = options;\n        this._root = root;\n        if (elements) {\n            for (let idx = 0; idx < elements.length; idx++) {\n                this[idx] = elements[idx];\n            }\n            this.length = elements.length;\n        }\n    }\n}\n/** Set a signature of the object. */\nCheerio.prototype.cheerio = '[cheerio object]';\n/*\n * Make cheerio an array-like object\n */\nCheerio.prototype.splice = Array.prototype.splice;\n// Support for (const element of $(...)) iteration:\nCheerio.prototype[Symbol.iterator] = Array.prototype[Symbol.iterator];\n// Plug in the API\nObject.assign(Cheerio.prototype, _api_attributes_js__WEBPACK_IMPORTED_MODULE_0__, _api_traversing_js__WEBPACK_IMPORTED_MODULE_1__, _api_manipulation_js__WEBPACK_IMPORTED_MODULE_2__, _api_css_js__WEBPACK_IMPORTED_MODULE_3__, _api_forms_js__WEBPACK_IMPORTED_MODULE_4__, _api_extract_js__WEBPACK_IMPORTED_MODULE_5__);\n//# sourceMappingURL=cheerio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/cheerio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* reexport safe */ _static_js__WEBPACK_IMPORTED_MODULE_1__.contains),\n/* harmony export */   decodeStream: () => (/* binding */ decodeStream),\n/* harmony export */   fromURL: () => (/* binding */ fromURL),\n/* harmony export */   load: () => (/* reexport safe */ _load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load),\n/* harmony export */   loadBuffer: () => (/* binding */ loadBuffer),\n/* harmony export */   merge: () => (/* reexport safe */ _static_js__WEBPACK_IMPORTED_MODULE_1__.merge),\n/* harmony export */   stringStream: () => (/* binding */ stringStream)\n/* harmony export */ });\n/* harmony import */ var _load_parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./load-parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/load-parse.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! parse5-htmlparser2-tree-adapter */ \"(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/* harmony import */ var parse5_parser_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! parse5-parser-stream */ \"(rsc)/./node_modules/parse5-parser-stream/dist/index.js\");\n/* harmony import */ var encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! encoding-sniffer */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\");\n/* harmony import */ var undici__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! undici */ \"(rsc)/./node_modules/undici/index.js\");\n/* harmony import */ var whatwg_mimetype__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! whatwg-mimetype */ \"(rsc)/./node_modules/whatwg-mimetype/lib/mime-type.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n/**\n * @file Batteries-included version of Cheerio. This module includes several\n *   convenience methods for loading documents from various sources.\n */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Sniffs the encoding of a buffer, then creates a querying function bound to a\n * document created from the buffer.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n *\n * const buffer = fs.readFileSync('index.html');\n * const $ = cheerio.loadBuffer(buffer);\n * ```\n *\n * @param buffer - The buffer to sniff the encoding of.\n * @param options - The options to pass to Cheerio.\n * @returns The loaded document.\n */\nfunction loadBuffer(buffer, options = {}) {\n    const opts = (0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(options);\n    const str = (0,encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__.decodeBuffer)(buffer, {\n        defaultEncoding: (opts === null || opts === void 0 ? void 0 : opts.xmlMode) ? 'utf8' : 'windows-1252',\n        ...options.encoding,\n    });\n    return (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(str, opts);\n}\nfunction _stringStream(options, cb) {\n    var _a;\n    if (options === null || options === void 0 ? void 0 : options._useHtmlParser2) {\n        const parser = htmlparser2__WEBPACK_IMPORTED_MODULE_3__.createDocumentStream((err, document) => cb(err, (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(document, options)), options);\n        return new node_stream__WEBPACK_IMPORTED_MODULE_8__.Writable({\n            decodeStrings: false,\n            write(chunk, _encoding, callback) {\n                if (typeof chunk !== 'string') {\n                    throw new TypeError('Expected a string');\n                }\n                parser.write(chunk);\n                callback();\n            },\n            final(callback) {\n                parser.end();\n                callback();\n            },\n        });\n    }\n    options !== null && options !== void 0 ? options : (options = {});\n    (_a = options.treeAdapter) !== null && _a !== void 0 ? _a : (options.treeAdapter = parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter);\n    if (options.scriptingEnabled !== false) {\n        options.scriptingEnabled = true;\n    }\n    const stream = new parse5_parser_stream__WEBPACK_IMPORTED_MODULE_4__.ParserStream(options);\n    (0,node_stream__WEBPACK_IMPORTED_MODULE_8__.finished)(stream, (err) => cb(err, (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(stream.document, options)));\n    return stream;\n}\n/**\n * Creates a stream that parses a sequence of strings into a document.\n *\n * The stream is a `Writable` stream that accepts strings. When the stream is\n * finished, the callback is called with the loaded document.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n * import * as fs from 'fs';\n *\n * const writeStream = cheerio.stringStream({}, (err, $) => {\n *   if (err) {\n *     // Handle error\n *   }\n *\n *   console.log($('h1').text());\n *   // Output: Hello, world!\n * });\n *\n * fs.createReadStream('my-document.html', { encoding: 'utf8' }).pipe(\n *   writeStream,\n * );\n * ```\n *\n * @param options - The options to pass to Cheerio.\n * @param cb - The callback to call when the stream is finished.\n * @returns The writable stream.\n */\nfunction stringStream(options, cb) {\n    return _stringStream((0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(options), cb);\n}\n/**\n * Parses a stream of buffers into a document.\n *\n * The stream is a `Writable` stream that accepts buffers. When the stream is\n * finished, the callback is called with the loaded document.\n *\n * @category Loading\n * @param options - The options to pass to Cheerio.\n * @param cb - The callback to call when the stream is finished.\n * @returns The writable stream.\n */\nfunction decodeStream(options, cb) {\n    var _a;\n    const { encoding = {}, ...cheerioOptions } = options;\n    const opts = (0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(cheerioOptions);\n    // Set the default encoding to UTF-8 for XML mode\n    (_a = encoding.defaultEncoding) !== null && _a !== void 0 ? _a : (encoding.defaultEncoding = (opts === null || opts === void 0 ? void 0 : opts.xmlMode) ? 'utf8' : 'windows-1252');\n    const decodeStream = new encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__.DecodeStream(encoding);\n    const loadStream = _stringStream(opts, cb);\n    decodeStream.pipe(loadStream);\n    return decodeStream;\n}\nconst defaultRequestOptions = {\n    method: 'GET',\n    // Set an Accept header\n    headers: {\n        accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n    },\n};\n/**\n * `fromURL` loads a document from a URL.\n *\n * By default, redirects are allowed and non-2xx responses are rejected.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n *\n * const $ = await cheerio.fromURL('https://example.com');\n * ```\n *\n * @param url - The URL to load the document from.\n * @param options - The options to pass to Cheerio.\n * @returns The loaded document.\n */\nasync function fromURL(url, options = {}) {\n    const { requestOptions = defaultRequestOptions, encoding = {}, ...cheerioOptions } = options;\n    let undiciStream;\n    // Add headers if none were supplied.\n    const urlObject = typeof url === 'string' ? new URL(url) : url;\n    const streamOptions = {\n        headers: defaultRequestOptions.headers,\n        path: urlObject.pathname + urlObject.search,\n        ...requestOptions,\n    };\n    const promise = new Promise((resolve, reject) => {\n        undiciStream = new undici__WEBPACK_IMPORTED_MODULE_6__.Client(urlObject.origin)\n            .compose(undici__WEBPACK_IMPORTED_MODULE_6__.interceptors.redirect({ maxRedirections: 5 }))\n            .stream(streamOptions, (res) => {\n            var _a, _b;\n            if (res.statusCode < 200 || res.statusCode >= 300) {\n                throw new undici__WEBPACK_IMPORTED_MODULE_6__.errors.ResponseError('Response Error', res.statusCode, {\n                    headers: res.headers,\n                });\n            }\n            const contentTypeHeader = (_a = res.headers['content-type']) !== null && _a !== void 0 ? _a : 'text/html';\n            const mimeType = new whatwg_mimetype__WEBPACK_IMPORTED_MODULE_7__(Array.isArray(contentTypeHeader)\n                ? contentTypeHeader[0]\n                : contentTypeHeader);\n            if (!mimeType.isHTML() && !mimeType.isXML()) {\n                throw new RangeError(`The content-type \"${mimeType.essence}\" is neither HTML nor XML.`);\n            }\n            // Forward the charset from the header to the decodeStream.\n            encoding.transportLayerEncodingLabel =\n                mimeType.parameters.get('charset');\n            /*\n             * If we allow redirects, we will have entries in the history.\n             * The last entry will be the final URL.\n             */\n            const history = (_b = res.context) === null || _b === void 0 ? void 0 : _b.history;\n            // Set the `baseURI` to the final URL.\n            const baseURI = history ? history[history.length - 1] : urlObject;\n            const opts = {\n                encoding,\n                // Set XML mode based on the MIME type.\n                xmlMode: mimeType.isXML(),\n                baseURI,\n                ...cheerioOptions,\n            };\n            return decodeStream(opts, (err, $) => (err ? reject(err) : resolve($)));\n        });\n    });\n    // Let's make sure the request is completed before returning the promise.\n    await undiciStream;\n    return promise;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY2hlZXJpby9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dDO0FBQ2M7QUFDa0M7QUFDckM7QUFDeUI7QUFDTDtBQUM5QjtBQUNNO0FBQ1U7QUFDRjtBQUNSO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLHdDQUF3QztBQUMvQyxpQkFBaUIsMkRBQWM7QUFDL0IsZ0JBQWdCLDhEQUFZO0FBQzVCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsV0FBVyxvREFBSTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDZEQUFnQyw0QkFBNEIsb0RBQUk7QUFDdkYsbUJBQW1CLGlEQUFRO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQSxvRUFBb0U7QUFDcEUsdUZBQXVGLG9FQUFrQjtBQUN6RztBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsOERBQVk7QUFDbkMsSUFBSSxxREFBUSwwQkFBMEIsb0RBQUk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLDZDQUE2QyxrQkFBa0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AseUJBQXlCLDJEQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxZQUFZLGFBQWEsc0JBQXNCO0FBQy9DLGlCQUFpQiwyREFBYztBQUMvQjtBQUNBO0FBQ0EsNkJBQTZCLDBEQUFZO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsVUFBVTtBQUMzRSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyx3Q0FBd0M7QUFDL0MsWUFBWSxxREFBcUQsc0JBQXNCO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwwQ0FBYTtBQUN4QyxxQkFBcUIsZ0RBQW1CLFlBQVksb0JBQW9CO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwQ0FBYTtBQUN2QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsaUNBQWlDLDRDQUFRO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxpQkFBaUI7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRG9jdW1lbnRzXFwzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxjaGVlcmlvXFxkaXN0XFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGZpbGUgQmF0dGVyaWVzLWluY2x1ZGVkIHZlcnNpb24gb2YgQ2hlZXJpby4gVGhpcyBtb2R1bGUgaW5jbHVkZXMgc2V2ZXJhbFxuICogICBjb252ZW5pZW5jZSBtZXRob2RzIGZvciBsb2FkaW5nIGRvY3VtZW50cyBmcm9tIHZhcmlvdXMgc291cmNlcy5cbiAqL1xuZXhwb3J0ICogZnJvbSAnLi9sb2FkLXBhcnNlLmpzJztcbmV4cG9ydCB7IGNvbnRhaW5zLCBtZXJnZSB9IGZyb20gJy4vc3RhdGljLmpzJztcbmltcG9ydCB7IGFkYXB0ZXIgYXMgaHRtbHBhcnNlcjJBZGFwdGVyIH0gZnJvbSAncGFyc2U1LWh0bWxwYXJzZXIyLXRyZWUtYWRhcHRlcic7XG5pbXBvcnQgKiBhcyBodG1scGFyc2VyMiBmcm9tICdodG1scGFyc2VyMic7XG5pbXBvcnQgeyBQYXJzZXJTdHJlYW0gYXMgUGFyc2U1U3RyZWFtIH0gZnJvbSAncGFyc2U1LXBhcnNlci1zdHJlYW0nO1xuaW1wb3J0IHsgZGVjb2RlQnVmZmVyLCBEZWNvZGVTdHJlYW0sIH0gZnJvbSAnZW5jb2Rpbmctc25pZmZlcic7XG5pbXBvcnQgKiBhcyB1bmRpY2kgZnJvbSAndW5kaWNpJztcbmltcG9ydCBNSU1FVHlwZSBmcm9tICd3aGF0d2ctbWltZXR5cGUnO1xuaW1wb3J0IHsgV3JpdGFibGUsIGZpbmlzaGVkIH0gZnJvbSAnbm9kZTpzdHJlYW0nO1xuaW1wb3J0IHsgZmxhdHRlbk9wdGlvbnMsIH0gZnJvbSAnLi9vcHRpb25zLmpzJztcbmltcG9ydCB7IGxvYWQgfSBmcm9tICcuL2xvYWQtcGFyc2UuanMnO1xuLyoqXG4gKiBTbmlmZnMgdGhlIGVuY29kaW5nIG9mIGEgYnVmZmVyLCB0aGVuIGNyZWF0ZXMgYSBxdWVyeWluZyBmdW5jdGlvbiBib3VuZCB0byBhXG4gKiBkb2N1bWVudCBjcmVhdGVkIGZyb20gdGhlIGJ1ZmZlci5cbiAqXG4gKiBAY2F0ZWdvcnkgTG9hZGluZ1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGBqc1xuICogaW1wb3J0ICogYXMgY2hlZXJpbyBmcm9tICdjaGVlcmlvJztcbiAqXG4gKiBjb25zdCBidWZmZXIgPSBmcy5yZWFkRmlsZVN5bmMoJ2luZGV4Lmh0bWwnKTtcbiAqIGNvbnN0ICQgPSBjaGVlcmlvLmxvYWRCdWZmZXIoYnVmZmVyKTtcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBidWZmZXIgLSBUaGUgYnVmZmVyIHRvIHNuaWZmIHRoZSBlbmNvZGluZyBvZi5cbiAqIEBwYXJhbSBvcHRpb25zIC0gVGhlIG9wdGlvbnMgdG8gcGFzcyB0byBDaGVlcmlvLlxuICogQHJldHVybnMgVGhlIGxvYWRlZCBkb2N1bWVudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGxvYWRCdWZmZXIoYnVmZmVyLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBvcHRzID0gZmxhdHRlbk9wdGlvbnMob3B0aW9ucyk7XG4gICAgY29uc3Qgc3RyID0gZGVjb2RlQnVmZmVyKGJ1ZmZlciwge1xuICAgICAgICBkZWZhdWx0RW5jb2Rpbmc6IChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMueG1sTW9kZSkgPyAndXRmOCcgOiAnd2luZG93cy0xMjUyJyxcbiAgICAgICAgLi4ub3B0aW9ucy5lbmNvZGluZyxcbiAgICB9KTtcbiAgICByZXR1cm4gbG9hZChzdHIsIG9wdHMpO1xufVxuZnVuY3Rpb24gX3N0cmluZ1N0cmVhbShvcHRpb25zLCBjYikge1xuICAgIHZhciBfYTtcbiAgICBpZiAob3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLl91c2VIdG1sUGFyc2VyMikge1xuICAgICAgICBjb25zdCBwYXJzZXIgPSBodG1scGFyc2VyMi5jcmVhdGVEb2N1bWVudFN0cmVhbSgoZXJyLCBkb2N1bWVudCkgPT4gY2IoZXJyLCBsb2FkKGRvY3VtZW50LCBvcHRpb25zKSksIG9wdGlvbnMpO1xuICAgICAgICByZXR1cm4gbmV3IFdyaXRhYmxlKHtcbiAgICAgICAgICAgIGRlY29kZVN0cmluZ3M6IGZhbHNlLFxuICAgICAgICAgICAgd3JpdGUoY2h1bmssIF9lbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGNodW5rICE9PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdFeHBlY3RlZCBhIHN0cmluZycpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBwYXJzZXIud3JpdGUoY2h1bmspO1xuICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZmluYWwoY2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICBwYXJzZXIuZW5kKCk7XG4gICAgICAgICAgICAgICAgY2FsbGJhY2soKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBvcHRpb25zICE9PSBudWxsICYmIG9wdGlvbnMgIT09IHZvaWQgMCA/IG9wdGlvbnMgOiAob3B0aW9ucyA9IHt9KTtcbiAgICAoX2EgPSBvcHRpb25zLnRyZWVBZGFwdGVyKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAob3B0aW9ucy50cmVlQWRhcHRlciA9IGh0bWxwYXJzZXIyQWRhcHRlcik7XG4gICAgaWYgKG9wdGlvbnMuc2NyaXB0aW5nRW5hYmxlZCAhPT0gZmFsc2UpIHtcbiAgICAgICAgb3B0aW9ucy5zY3JpcHRpbmdFbmFibGVkID0gdHJ1ZTtcbiAgICB9XG4gICAgY29uc3Qgc3RyZWFtID0gbmV3IFBhcnNlNVN0cmVhbShvcHRpb25zKTtcbiAgICBmaW5pc2hlZChzdHJlYW0sIChlcnIpID0+IGNiKGVyciwgbG9hZChzdHJlYW0uZG9jdW1lbnQsIG9wdGlvbnMpKSk7XG4gICAgcmV0dXJuIHN0cmVhbTtcbn1cbi8qKlxuICogQ3JlYXRlcyBhIHN0cmVhbSB0aGF0IHBhcnNlcyBhIHNlcXVlbmNlIG9mIHN0cmluZ3MgaW50byBhIGRvY3VtZW50LlxuICpcbiAqIFRoZSBzdHJlYW0gaXMgYSBgV3JpdGFibGVgIHN0cmVhbSB0aGF0IGFjY2VwdHMgc3RyaW5ncy4gV2hlbiB0aGUgc3RyZWFtIGlzXG4gKiBmaW5pc2hlZCwgdGhlIGNhbGxiYWNrIGlzIGNhbGxlZCB3aXRoIHRoZSBsb2FkZWQgZG9jdW1lbnQuXG4gKlxuICogQGNhdGVnb3J5IExvYWRpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqIGltcG9ydCAqIGFzIGNoZWVyaW8gZnJvbSAnY2hlZXJpbyc7XG4gKiBpbXBvcnQgKiBhcyBmcyBmcm9tICdmcyc7XG4gKlxuICogY29uc3Qgd3JpdGVTdHJlYW0gPSBjaGVlcmlvLnN0cmluZ1N0cmVhbSh7fSwgKGVyciwgJCkgPT4ge1xuICogICBpZiAoZXJyKSB7XG4gKiAgICAgLy8gSGFuZGxlIGVycm9yXG4gKiAgIH1cbiAqXG4gKiAgIGNvbnNvbGUubG9nKCQoJ2gxJykudGV4dCgpKTtcbiAqICAgLy8gT3V0cHV0OiBIZWxsbywgd29ybGQhXG4gKiB9KTtcbiAqXG4gKiBmcy5jcmVhdGVSZWFkU3RyZWFtKCdteS1kb2N1bWVudC5odG1sJywgeyBlbmNvZGluZzogJ3V0ZjgnIH0pLnBpcGUoXG4gKiAgIHdyaXRlU3RyZWFtLFxuICogKTtcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBvcHRpb25zIC0gVGhlIG9wdGlvbnMgdG8gcGFzcyB0byBDaGVlcmlvLlxuICogQHBhcmFtIGNiIC0gVGhlIGNhbGxiYWNrIHRvIGNhbGwgd2hlbiB0aGUgc3RyZWFtIGlzIGZpbmlzaGVkLlxuICogQHJldHVybnMgVGhlIHdyaXRhYmxlIHN0cmVhbS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ1N0cmVhbShvcHRpb25zLCBjYikge1xuICAgIHJldHVybiBfc3RyaW5nU3RyZWFtKGZsYXR0ZW5PcHRpb25zKG9wdGlvbnMpLCBjYik7XG59XG4vKipcbiAqIFBhcnNlcyBhIHN0cmVhbSBvZiBidWZmZXJzIGludG8gYSBkb2N1bWVudC5cbiAqXG4gKiBUaGUgc3RyZWFtIGlzIGEgYFdyaXRhYmxlYCBzdHJlYW0gdGhhdCBhY2NlcHRzIGJ1ZmZlcnMuIFdoZW4gdGhlIHN0cmVhbSBpc1xuICogZmluaXNoZWQsIHRoZSBjYWxsYmFjayBpcyBjYWxsZWQgd2l0aCB0aGUgbG9hZGVkIGRvY3VtZW50LlxuICpcbiAqIEBjYXRlZ29yeSBMb2FkaW5nXG4gKiBAcGFyYW0gb3B0aW9ucyAtIFRoZSBvcHRpb25zIHRvIHBhc3MgdG8gQ2hlZXJpby5cbiAqIEBwYXJhbSBjYiAtIFRoZSBjYWxsYmFjayB0byBjYWxsIHdoZW4gdGhlIHN0cmVhbSBpcyBmaW5pc2hlZC5cbiAqIEByZXR1cm5zIFRoZSB3cml0YWJsZSBzdHJlYW0uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZWNvZGVTdHJlYW0ob3B0aW9ucywgY2IpIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgeyBlbmNvZGluZyA9IHt9LCAuLi5jaGVlcmlvT3B0aW9ucyB9ID0gb3B0aW9ucztcbiAgICBjb25zdCBvcHRzID0gZmxhdHRlbk9wdGlvbnMoY2hlZXJpb09wdGlvbnMpO1xuICAgIC8vIFNldCB0aGUgZGVmYXVsdCBlbmNvZGluZyB0byBVVEYtOCBmb3IgWE1MIG1vZGVcbiAgICAoX2EgPSBlbmNvZGluZy5kZWZhdWx0RW5jb2RpbmcpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IChlbmNvZGluZy5kZWZhdWx0RW5jb2RpbmcgPSAob3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLnhtbE1vZGUpID8gJ3V0ZjgnIDogJ3dpbmRvd3MtMTI1MicpO1xuICAgIGNvbnN0IGRlY29kZVN0cmVhbSA9IG5ldyBEZWNvZGVTdHJlYW0oZW5jb2RpbmcpO1xuICAgIGNvbnN0IGxvYWRTdHJlYW0gPSBfc3RyaW5nU3RyZWFtKG9wdHMsIGNiKTtcbiAgICBkZWNvZGVTdHJlYW0ucGlwZShsb2FkU3RyZWFtKTtcbiAgICByZXR1cm4gZGVjb2RlU3RyZWFtO1xufVxuY29uc3QgZGVmYXVsdFJlcXVlc3RPcHRpb25zID0ge1xuICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgLy8gU2V0IGFuIEFjY2VwdCBoZWFkZXJcbiAgICBoZWFkZXJzOiB7XG4gICAgICAgIGFjY2VwdDogJ3RleHQvaHRtbCxhcHBsaWNhdGlvbi94aHRtbCt4bWwsYXBwbGljYXRpb24veG1sO3E9MC45LCovKjtxPTAuOCcsXG4gICAgfSxcbn07XG4vKipcbiAqIGBmcm9tVVJMYCBsb2FkcyBhIGRvY3VtZW50IGZyb20gYSBVUkwuXG4gKlxuICogQnkgZGVmYXVsdCwgcmVkaXJlY3RzIGFyZSBhbGxvd2VkIGFuZCBub24tMnh4IHJlc3BvbnNlcyBhcmUgcmVqZWN0ZWQuXG4gKlxuICogQGNhdGVnb3J5IExvYWRpbmdcbiAqIEBleGFtcGxlXG4gKlxuICogYGBganNcbiAqIGltcG9ydCAqIGFzIGNoZWVyaW8gZnJvbSAnY2hlZXJpbyc7XG4gKlxuICogY29uc3QgJCA9IGF3YWl0IGNoZWVyaW8uZnJvbVVSTCgnaHR0cHM6Ly9leGFtcGxlLmNvbScpO1xuICogYGBgXG4gKlxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgdG8gbG9hZCB0aGUgZG9jdW1lbnQgZnJvbS5cbiAqIEBwYXJhbSBvcHRpb25zIC0gVGhlIG9wdGlvbnMgdG8gcGFzcyB0byBDaGVlcmlvLlxuICogQHJldHVybnMgVGhlIGxvYWRlZCBkb2N1bWVudC5cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGZyb21VUkwodXJsLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IHJlcXVlc3RPcHRpb25zID0gZGVmYXVsdFJlcXVlc3RPcHRpb25zLCBlbmNvZGluZyA9IHt9LCAuLi5jaGVlcmlvT3B0aW9ucyB9ID0gb3B0aW9ucztcbiAgICBsZXQgdW5kaWNpU3RyZWFtO1xuICAgIC8vIEFkZCBoZWFkZXJzIGlmIG5vbmUgd2VyZSBzdXBwbGllZC5cbiAgICBjb25zdCB1cmxPYmplY3QgPSB0eXBlb2YgdXJsID09PSAnc3RyaW5nJyA/IG5ldyBVUkwodXJsKSA6IHVybDtcbiAgICBjb25zdCBzdHJlYW1PcHRpb25zID0ge1xuICAgICAgICBoZWFkZXJzOiBkZWZhdWx0UmVxdWVzdE9wdGlvbnMuaGVhZGVycyxcbiAgICAgICAgcGF0aDogdXJsT2JqZWN0LnBhdGhuYW1lICsgdXJsT2JqZWN0LnNlYXJjaCxcbiAgICAgICAgLi4ucmVxdWVzdE9wdGlvbnMsXG4gICAgfTtcbiAgICBjb25zdCBwcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICB1bmRpY2lTdHJlYW0gPSBuZXcgdW5kaWNpLkNsaWVudCh1cmxPYmplY3Qub3JpZ2luKVxuICAgICAgICAgICAgLmNvbXBvc2UodW5kaWNpLmludGVyY2VwdG9ycy5yZWRpcmVjdCh7IG1heFJlZGlyZWN0aW9uczogNSB9KSlcbiAgICAgICAgICAgIC5zdHJlYW0oc3RyZWFtT3B0aW9ucywgKHJlcykgPT4ge1xuICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgIGlmIChyZXMuc3RhdHVzQ29kZSA8IDIwMCB8fCByZXMuc3RhdHVzQ29kZSA+PSAzMDApIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgdW5kaWNpLmVycm9ycy5SZXNwb25zZUVycm9yKCdSZXNwb25zZSBFcnJvcicsIHJlcy5zdGF0dXNDb2RlLCB7XG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHJlcy5oZWFkZXJzLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgY29udGVudFR5cGVIZWFkZXIgPSAoX2EgPSByZXMuaGVhZGVyc1snY29udGVudC10eXBlJ10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6ICd0ZXh0L2h0bWwnO1xuICAgICAgICAgICAgY29uc3QgbWltZVR5cGUgPSBuZXcgTUlNRVR5cGUoQXJyYXkuaXNBcnJheShjb250ZW50VHlwZUhlYWRlcilcbiAgICAgICAgICAgICAgICA/IGNvbnRlbnRUeXBlSGVhZGVyWzBdXG4gICAgICAgICAgICAgICAgOiBjb250ZW50VHlwZUhlYWRlcik7XG4gICAgICAgICAgICBpZiAoIW1pbWVUeXBlLmlzSFRNTCgpICYmICFtaW1lVHlwZS5pc1hNTCgpKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoYFRoZSBjb250ZW50LXR5cGUgXCIke21pbWVUeXBlLmVzc2VuY2V9XCIgaXMgbmVpdGhlciBIVE1MIG5vciBYTUwuYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBGb3J3YXJkIHRoZSBjaGFyc2V0IGZyb20gdGhlIGhlYWRlciB0byB0aGUgZGVjb2RlU3RyZWFtLlxuICAgICAgICAgICAgZW5jb2RpbmcudHJhbnNwb3J0TGF5ZXJFbmNvZGluZ0xhYmVsID1cbiAgICAgICAgICAgICAgICBtaW1lVHlwZS5wYXJhbWV0ZXJzLmdldCgnY2hhcnNldCcpO1xuICAgICAgICAgICAgLypcbiAgICAgICAgICAgICAqIElmIHdlIGFsbG93IHJlZGlyZWN0cywgd2Ugd2lsbCBoYXZlIGVudHJpZXMgaW4gdGhlIGhpc3RvcnkuXG4gICAgICAgICAgICAgKiBUaGUgbGFzdCBlbnRyeSB3aWxsIGJlIHRoZSBmaW5hbCBVUkwuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGNvbnN0IGhpc3RvcnkgPSAoX2IgPSByZXMuY29udGV4dCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmhpc3Rvcnk7XG4gICAgICAgICAgICAvLyBTZXQgdGhlIGBiYXNlVVJJYCB0byB0aGUgZmluYWwgVVJMLlxuICAgICAgICAgICAgY29uc3QgYmFzZVVSSSA9IGhpc3RvcnkgPyBoaXN0b3J5W2hpc3RvcnkubGVuZ3RoIC0gMV0gOiB1cmxPYmplY3Q7XG4gICAgICAgICAgICBjb25zdCBvcHRzID0ge1xuICAgICAgICAgICAgICAgIGVuY29kaW5nLFxuICAgICAgICAgICAgICAgIC8vIFNldCBYTUwgbW9kZSBiYXNlZCBvbiB0aGUgTUlNRSB0eXBlLlxuICAgICAgICAgICAgICAgIHhtbE1vZGU6IG1pbWVUeXBlLmlzWE1MKCksXG4gICAgICAgICAgICAgICAgYmFzZVVSSSxcbiAgICAgICAgICAgICAgICAuLi5jaGVlcmlvT3B0aW9ucyxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICByZXR1cm4gZGVjb2RlU3RyZWFtKG9wdHMsIChlcnIsICQpID0+IChlcnIgPyByZWplY3QoZXJyKSA6IHJlc29sdmUoJCkpKTtcbiAgICAgICAgfSk7XG4gICAgfSk7XG4gICAgLy8gTGV0J3MgbWFrZSBzdXJlIHRoZSByZXF1ZXN0IGlzIGNvbXBsZXRlZCBiZWZvcmUgcmV0dXJuaW5nIHRoZSBwcm9taXNlLlxuICAgIGF3YWl0IHVuZGljaVN0cmVhbTtcbiAgICByZXR1cm4gcHJvbWlzZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/load-parse.js":
/*!*****************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/load-parse.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   load: () => (/* binding */ load)\n/* harmony export */ });\n/* harmony import */ var _load_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./load.js */ \"(rsc)/./node_modules/cheerio/dist/esm/load.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parse.js\");\n/* harmony import */ var _parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parsers/parse5-adapter.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n\n\n\n\n\nconst parse = (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.getParse)((content, options, isDocument, context) => options._useHtmlParser2\n    ? (0,htmlparser2__WEBPACK_IMPORTED_MODULE_4__.parseDocument)(content, options)\n    : (0,_parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__.parseWithParse5)(content, options, isDocument, context));\n// Duplicate docs due to https://github.com/TypeStrong/typedoc/issues/1616\n/**\n * Create a querying function, bound to a document created from the provided\n * markup.\n *\n * Note that similar to web browser contexts, this operation may introduce\n * `<html>`, `<head>`, and `<body>` elements; set `isDocument` to `false` to\n * switch to fragment mode and disable this.\n *\n * @category Loading\n * @param content - Markup to be loaded.\n * @param options - Options for the created instance.\n * @param isDocument - Allows parser to be switched to fragment mode.\n * @returns The loaded document.\n * @see {@link https://cheerio.js.org/docs/basics/loading#load} for additional usage information.\n */\nconst load = (0,_load_js__WEBPACK_IMPORTED_MODULE_0__.getLoad)(parse, (dom, options) => options._useHtmlParser2\n    ? (0,dom_serializer__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dom, options)\n    : (0,_parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__.renderWithParse5)(dom));\n//# sourceMappingURL=load-parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/load-parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/load.js":
/*!***********************************************!*\
  !*** ./node_modules/cheerio/dist/esm/load.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLoad: () => (/* binding */ getLoad)\n/* harmony export */ });\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _cheerio_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cheerio.js */ \"(rsc)/./node_modules/cheerio/dist/esm/cheerio.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n\n\n\n\n\nfunction getLoad(parse, render) {\n    /**\n     * Create a querying function, bound to a document created from the provided\n     * markup.\n     *\n     * Note that similar to web browser contexts, this operation may introduce\n     * `<html>`, `<head>`, and `<body>` elements; set `isDocument` to `false` to\n     * switch to fragment mode and disable this.\n     *\n     * @param content - Markup to be loaded.\n     * @param options - Options for the created instance.\n     * @param isDocument - Allows parser to be switched to fragment mode.\n     * @returns The loaded document.\n     * @see {@link https://cheerio.js.org/docs/basics/loading#load} for additional usage information.\n     */\n    return function load(content, options, isDocument = true) {\n        if (content == null) {\n            throw new Error('cheerio.load() expects a string');\n        }\n        const internalOpts = (0,_options_js__WEBPACK_IMPORTED_MODULE_0__.flattenOptions)(options);\n        const initialRoot = parse(content, internalOpts, isDocument, null);\n        /**\n         * Create an extended class here, so that extensions only live on one\n         * instance.\n         */\n        class LoadedCheerio extends _cheerio_js__WEBPACK_IMPORTED_MODULE_2__.Cheerio {\n            _make(selector, context) {\n                const cheerio = initialize(selector, context);\n                cheerio.prevObject = this;\n                return cheerio;\n            }\n            _parse(content, options, isDocument, context) {\n                return parse(content, options, isDocument, context);\n            }\n            _render(dom) {\n                return render(dom, this.options);\n            }\n        }\n        function initialize(selector, context, root = initialRoot, opts) {\n            // $($)\n            if (selector && (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(selector))\n                return selector;\n            const options = (0,_options_js__WEBPACK_IMPORTED_MODULE_0__.flattenOptions)(opts, internalOpts);\n            const r = typeof root === 'string'\n                ? [parse(root, options, false, null)]\n                : 'length' in root\n                    ? root\n                    : [root];\n            const rootInstance = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(r)\n                ? r\n                : new LoadedCheerio(r, null, options);\n            // Add a cyclic reference, so that calling methods on `_root` never fails.\n            rootInstance._root = rootInstance;\n            // $(), $(null), $(undefined), $(false)\n            if (!selector) {\n                return new LoadedCheerio(undefined, rootInstance, options);\n            }\n            const elements = typeof selector === 'string' && (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(selector)\n                ? // $(<html>)\n                    parse(selector, options, false, null).children\n                : isNode(selector)\n                    ? // $(dom)\n                        [selector]\n                    : Array.isArray(selector)\n                        ? // $([dom])\n                            selector\n                        : undefined;\n            const instance = new LoadedCheerio(elements, rootInstance, options);\n            if (elements) {\n                return instance;\n            }\n            if (typeof selector !== 'string') {\n                throw new TypeError('Unexpected type of selector');\n            }\n            // We know that our selector is a string now.\n            let search = selector;\n            const searchContext = context\n                ? // If we don't have a context, maybe we have a root, from loading\n                    typeof context === 'string'\n                        ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(context)\n                            ? // $('li', '<ul>...</ul>')\n                                new LoadedCheerio([parse(context, options, false, null)], rootInstance, options)\n                            : // $('li', 'ul')\n                                ((search = `${context} ${search}`), rootInstance)\n                        : (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(context)\n                            ? // $('li', $)\n                                context\n                            : // $('li', node), $('li', [nodes])\n                                new LoadedCheerio(Array.isArray(context) ? context : [context], rootInstance, options)\n                : rootInstance;\n            // If we still don't have a context, return\n            if (!searchContext)\n                return instance;\n            /*\n             * #id, .class, tag\n             */\n            return searchContext.find(search);\n        }\n        // Add in static methods & properties\n        Object.assign(initialize, _static_js__WEBPACK_IMPORTED_MODULE_1__, {\n            load,\n            // `_root` and `_options` are used in static methods.\n            _root: initialRoot,\n            _options: internalOpts,\n            // Add `fn` for plugins\n            fn: LoadedCheerio.prototype,\n            // Add the prototype here to maintain `instanceof` behavior.\n            prototype: LoadedCheerio.prototype,\n        });\n        return initialize;\n    };\n}\nfunction isNode(obj) {\n    return (\n    // @ts-expect-error: TS doesn't know about the `name` property.\n    !!obj.name ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Root ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Text ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Comment);\n}\n//# sourceMappingURL=load.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/load.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/options.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/options.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenOptions: () => (/* binding */ flattenOptions)\n/* harmony export */ });\nconst defaultOpts = {\n    _useHtmlParser2: false,\n};\n/**\n * Flatten the options for Cheerio.\n *\n * This will set `_useHtmlParser2` to true if `xml` is set to true.\n *\n * @param options - The options to flatten.\n * @param baseOptions - The base options to use.\n * @returns The flattened options.\n */\nfunction flattenOptions(options, baseOptions) {\n    if (!options) {\n        return baseOptions !== null && baseOptions !== void 0 ? baseOptions : defaultOpts;\n    }\n    const opts = {\n        _useHtmlParser2: !!options.xmlMode,\n        ...baseOptions,\n        ...options,\n    };\n    if (options.xml) {\n        opts._useHtmlParser2 = true;\n        opts.xmlMode = true;\n        if (options.xml !== true) {\n            Object.assign(opts, options.xml);\n        }\n    }\n    else if (options.xmlMode) {\n        opts._useHtmlParser2 = true;\n    }\n    return opts;\n}\n//# sourceMappingURL=options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY2hlZXJpby9kaXN0L2VzbS9vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEb2N1bWVudHNcXDNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGNoZWVyaW9cXGRpc3RcXGVzbVxcb3B0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBkZWZhdWx0T3B0cyA9IHtcbiAgICBfdXNlSHRtbFBhcnNlcjI6IGZhbHNlLFxufTtcbi8qKlxuICogRmxhdHRlbiB0aGUgb3B0aW9ucyBmb3IgQ2hlZXJpby5cbiAqXG4gKiBUaGlzIHdpbGwgc2V0IGBfdXNlSHRtbFBhcnNlcjJgIHRvIHRydWUgaWYgYHhtbGAgaXMgc2V0IHRvIHRydWUuXG4gKlxuICogQHBhcmFtIG9wdGlvbnMgLSBUaGUgb3B0aW9ucyB0byBmbGF0dGVuLlxuICogQHBhcmFtIGJhc2VPcHRpb25zIC0gVGhlIGJhc2Ugb3B0aW9ucyB0byB1c2UuXG4gKiBAcmV0dXJucyBUaGUgZmxhdHRlbmVkIG9wdGlvbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmbGF0dGVuT3B0aW9ucyhvcHRpb25zLCBiYXNlT3B0aW9ucykge1xuICAgIGlmICghb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gYmFzZU9wdGlvbnMgIT09IG51bGwgJiYgYmFzZU9wdGlvbnMgIT09IHZvaWQgMCA/IGJhc2VPcHRpb25zIDogZGVmYXVsdE9wdHM7XG4gICAgfVxuICAgIGNvbnN0IG9wdHMgPSB7XG4gICAgICAgIF91c2VIdG1sUGFyc2VyMjogISFvcHRpb25zLnhtbE1vZGUsXG4gICAgICAgIC4uLmJhc2VPcHRpb25zLFxuICAgICAgICAuLi5vcHRpb25zLFxuICAgIH07XG4gICAgaWYgKG9wdGlvbnMueG1sKSB7XG4gICAgICAgIG9wdHMuX3VzZUh0bWxQYXJzZXIyID0gdHJ1ZTtcbiAgICAgICAgb3B0cy54bWxNb2RlID0gdHJ1ZTtcbiAgICAgICAgaWYgKG9wdGlvbnMueG1sICE9PSB0cnVlKSB7XG4gICAgICAgICAgICBPYmplY3QuYXNzaWduKG9wdHMsIG9wdGlvbnMueG1sKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChvcHRpb25zLnhtbE1vZGUpIHtcbiAgICAgICAgb3B0cy5fdXNlSHRtbFBhcnNlcjIgPSB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gb3B0cztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW9wdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/options.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/parse.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/parse.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getParse: () => (/* binding */ getParse),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n/**\n * Get the parse function with options.\n *\n * @param parser - The parser function.\n * @returns The parse function with options.\n */\nfunction getParse(parser) {\n    /**\n     * Parse a HTML string or a node.\n     *\n     * @param content - The HTML string or node.\n     * @param options - The parser options.\n     * @param isDocument - If `content` is a document.\n     * @param context - The context node in the DOM tree.\n     * @returns The parsed document node.\n     */\n    return function parse(content, options, isDocument, context) {\n        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(content)) {\n            content = content.toString();\n        }\n        if (typeof content === 'string') {\n            return parser(content, options, isDocument, context);\n        }\n        const doc = content;\n        if (!Array.isArray(doc) && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDocument)(doc)) {\n            // If `doc` is already a root, just return it\n            return doc;\n        }\n        // Add conent to new root element\n        const root = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n        // Update the DOM using the root\n        update(doc, root);\n        return root;\n    };\n}\n/**\n * Update the dom structure, for one changed layer.\n *\n * @param newChilds - The new children.\n * @param parent - The new parent.\n * @returns The parent node.\n */\nfunction update(newChilds, parent) {\n    // Normalize\n    const arr = Array.isArray(newChilds) ? newChilds : [newChilds];\n    // Update parent\n    if (parent) {\n        parent.children = arr;\n    }\n    else {\n        parent = null;\n    }\n    // Update neighbors\n    for (let i = 0; i < arr.length; i++) {\n        const node = arr[i];\n        // Cleanly remove existing nodes from their previous structures.\n        if (node.parent && node.parent.children !== arr) {\n            (0,domutils__WEBPACK_IMPORTED_MODULE_0__.removeElement)(node);\n        }\n        if (parent) {\n            node.prev = arr[i - 1] || null;\n            node.next = arr[i + 1] || null;\n        }\n        else {\n            node.prev = node.next = null;\n        }\n        node.parent = parent;\n    }\n    return parent;\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY2hlZXJpby9kaXN0L2VzbS9wYXJzZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlDO0FBQzZCO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxzREFBZTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixnREFBUTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxZQUFZLHVEQUFhO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcY2hlZXJpb1xcZGlzdFxcZXNtXFxwYXJzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZW1vdmVFbGVtZW50IH0gZnJvbSAnZG9tdXRpbHMnO1xuaW1wb3J0IHsgRG9jdW1lbnQsIGlzRG9jdW1lbnQgYXMgY2hlY2tJc0RvY3VtZW50LCB9IGZyb20gJ2RvbWhhbmRsZXInO1xuLyoqXG4gKiBHZXQgdGhlIHBhcnNlIGZ1bmN0aW9uIHdpdGggb3B0aW9ucy5cbiAqXG4gKiBAcGFyYW0gcGFyc2VyIC0gVGhlIHBhcnNlciBmdW5jdGlvbi5cbiAqIEByZXR1cm5zIFRoZSBwYXJzZSBmdW5jdGlvbiB3aXRoIG9wdGlvbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRQYXJzZShwYXJzZXIpIHtcbiAgICAvKipcbiAgICAgKiBQYXJzZSBhIEhUTUwgc3RyaW5nIG9yIGEgbm9kZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBjb250ZW50IC0gVGhlIEhUTUwgc3RyaW5nIG9yIG5vZGUuXG4gICAgICogQHBhcmFtIG9wdGlvbnMgLSBUaGUgcGFyc2VyIG9wdGlvbnMuXG4gICAgICogQHBhcmFtIGlzRG9jdW1lbnQgLSBJZiBgY29udGVudGAgaXMgYSBkb2N1bWVudC5cbiAgICAgKiBAcGFyYW0gY29udGV4dCAtIFRoZSBjb250ZXh0IG5vZGUgaW4gdGhlIERPTSB0cmVlLlxuICAgICAqIEByZXR1cm5zIFRoZSBwYXJzZWQgZG9jdW1lbnQgbm9kZS5cbiAgICAgKi9cbiAgICByZXR1cm4gZnVuY3Rpb24gcGFyc2UoY29udGVudCwgb3B0aW9ucywgaXNEb2N1bWVudCwgY29udGV4dCkge1xuICAgICAgICBpZiAodHlwZW9mIEJ1ZmZlciAhPT0gJ3VuZGVmaW5lZCcgJiYgQnVmZmVyLmlzQnVmZmVyKGNvbnRlbnQpKSB7XG4gICAgICAgICAgICBjb250ZW50ID0gY29udGVudC50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29udGVudCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJzZXIoY29udGVudCwgb3B0aW9ucywgaXNEb2N1bWVudCwgY29udGV4dCk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZG9jID0gY29udGVudDtcbiAgICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGRvYykgJiYgY2hlY2tJc0RvY3VtZW50KGRvYykpIHtcbiAgICAgICAgICAgIC8vIElmIGBkb2NgIGlzIGFscmVhZHkgYSByb290LCBqdXN0IHJldHVybiBpdFxuICAgICAgICAgICAgcmV0dXJuIGRvYztcbiAgICAgICAgfVxuICAgICAgICAvLyBBZGQgY29uZW50IHRvIG5ldyByb290IGVsZW1lbnRcbiAgICAgICAgY29uc3Qgcm9vdCA9IG5ldyBEb2N1bWVudChbXSk7XG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgRE9NIHVzaW5nIHRoZSByb290XG4gICAgICAgIHVwZGF0ZShkb2MsIHJvb3QpO1xuICAgICAgICByZXR1cm4gcm9vdDtcbiAgICB9O1xufVxuLyoqXG4gKiBVcGRhdGUgdGhlIGRvbSBzdHJ1Y3R1cmUsIGZvciBvbmUgY2hhbmdlZCBsYXllci5cbiAqXG4gKiBAcGFyYW0gbmV3Q2hpbGRzIC0gVGhlIG5ldyBjaGlsZHJlbi5cbiAqIEBwYXJhbSBwYXJlbnQgLSBUaGUgbmV3IHBhcmVudC5cbiAqIEByZXR1cm5zIFRoZSBwYXJlbnQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZShuZXdDaGlsZHMsIHBhcmVudCkge1xuICAgIC8vIE5vcm1hbGl6ZVxuICAgIGNvbnN0IGFyciA9IEFycmF5LmlzQXJyYXkobmV3Q2hpbGRzKSA/IG5ld0NoaWxkcyA6IFtuZXdDaGlsZHNdO1xuICAgIC8vIFVwZGF0ZSBwYXJlbnRcbiAgICBpZiAocGFyZW50KSB7XG4gICAgICAgIHBhcmVudC5jaGlsZHJlbiA9IGFycjtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHBhcmVudCA9IG51bGw7XG4gICAgfVxuICAgIC8vIFVwZGF0ZSBuZWlnaGJvcnNcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFyci5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBub2RlID0gYXJyW2ldO1xuICAgICAgICAvLyBDbGVhbmx5IHJlbW92ZSBleGlzdGluZyBub2RlcyBmcm9tIHRoZWlyIHByZXZpb3VzIHN0cnVjdHVyZXMuXG4gICAgICAgIGlmIChub2RlLnBhcmVudCAmJiBub2RlLnBhcmVudC5jaGlsZHJlbiAhPT0gYXJyKSB7XG4gICAgICAgICAgICByZW1vdmVFbGVtZW50KG5vZGUpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwYXJlbnQpIHtcbiAgICAgICAgICAgIG5vZGUucHJldiA9IGFycltpIC0gMV0gfHwgbnVsbDtcbiAgICAgICAgICAgIG5vZGUubmV4dCA9IGFycltpICsgMV0gfHwgbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG5vZGUucHJldiA9IG5vZGUubmV4dCA9IG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgbm9kZS5wYXJlbnQgPSBwYXJlbnQ7XG4gICAgfVxuICAgIHJldHVybiBwYXJlbnQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYXJzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js":
/*!*****************************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseWithParse5: () => (/* binding */ parseWithParse5),\n/* harmony export */   renderWithParse5: () => (/* binding */ renderWithParse5)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! parse5-htmlparser2-tree-adapter */ \"(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\");\n\n\n\n/**\n * Parse the content with `parse5` in the context of the given `ParentNode`.\n *\n * @param content - The content to parse.\n * @param options - A set of options to use to parse.\n * @param isDocument - Whether to parse the content as a full HTML document.\n * @param context - The context in which to parse the content.\n * @returns The parsed content.\n */\nfunction parseWithParse5(content, options, isDocument, context) {\n    var _a;\n    (_a = options.treeAdapter) !== null && _a !== void 0 ? _a : (options.treeAdapter = parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter);\n    if (options.scriptingEnabled !== false) {\n        options.scriptingEnabled = true;\n    }\n    return isDocument\n        ? (0,parse5__WEBPACK_IMPORTED_MODULE_1__.parse)(content, options)\n        : (0,parse5__WEBPACK_IMPORTED_MODULE_1__.parseFragment)(context, content, options);\n}\nconst renderOpts = { treeAdapter: parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter };\n/**\n * Renders the given DOM tree with `parse5` and returns the result as a string.\n *\n * @param dom - The DOM tree to render.\n * @returns The rendered document.\n */\nfunction renderWithParse5(dom) {\n    /*\n     * `dom-serializer` passes over the special \"root\" node and renders the\n     * node's children in its place. To mimic this behavior with `parse5`, an\n     * equivalent operation must be applied to the input array.\n     */\n    const nodes = 'length' in dom ? dom : [dom];\n    for (let index = 0; index < nodes.length; index += 1) {\n        const node = nodes[index];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(node)) {\n            Array.prototype.splice.call(nodes, index, 1, ...node.children);\n        }\n    }\n    let result = '';\n    for (let index = 0; index < nodes.length; index += 1) {\n        const node = nodes[index];\n        result += (0,parse5__WEBPACK_IMPORTED_MODULE_1__.serializeOuter)(node, renderOpts);\n    }\n    return result;\n}\n//# sourceMappingURL=parse5-adapter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/static.js":
/*!*************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/static.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* binding */ contains),\n/* harmony export */   extract: () => (/* binding */ extract),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   merge: () => (/* binding */ merge),\n/* harmony export */   parseHTML: () => (/* binding */ parseHTML),\n/* harmony export */   root: () => (/* binding */ root),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n\n\n/**\n * Helper function to render a DOM.\n *\n * @param that - Cheerio instance to render.\n * @param dom - The DOM to render. Defaults to `that`'s root.\n * @param options - Options for rendering.\n * @returns The rendered document.\n */\nfunction render(that, dom, options) {\n    if (!that)\n        return '';\n    return that(dom !== null && dom !== void 0 ? dom : that._root.children, null, undefined, options).toString();\n}\n/**\n * Checks if a passed object is an options object.\n *\n * @param dom - Object to check if it is an options object.\n * @param options - Options object.\n * @returns Whether the object is an options object.\n */\nfunction isOptions(dom, options) {\n    return (!options &&\n        typeof dom === 'object' &&\n        dom != null &&\n        !('length' in dom) &&\n        !('type' in dom));\n}\nfunction html(dom, options) {\n    /*\n     * Be flexible about parameters, sometimes we call html(),\n     * with options as only parameter\n     * check dom argument for dom element specific properties\n     * assume there is no 'length' or 'type' properties in the options object\n     */\n    const toRender = isOptions(dom) ? ((options = dom), undefined) : dom;\n    /*\n     * Sometimes `$.html()` is used without preloading html,\n     * so fallback non-existing options to the default ones.\n     */\n    const opts = {\n        ...this === null || this === void 0 ? void 0 : this._options,\n        ...(0,_options_js__WEBPACK_IMPORTED_MODULE_1__.flattenOptions)(options),\n    };\n    return render(this, toRender, opts);\n}\n/**\n * Render the document as XML.\n *\n * @category Static\n * @param dom - Element to render.\n * @returns THe rendered document.\n */\nfunction xml(dom) {\n    const options = { ...this._options, xmlMode: true };\n    return render(this, dom, options);\n}\n/**\n * Render the document as text.\n *\n * This returns the `textContent` of the passed elements. The result will\n * include the contents of `<script>` and `<style>` elements. To avoid this, use\n * `.prop('innerText')` instead.\n *\n * @category Static\n * @param elements - Elements to render.\n * @returns The rendered document.\n */\nfunction text(elements) {\n    const elems = elements !== null && elements !== void 0 ? elements : (this ? this.root() : []);\n    let ret = '';\n    for (let i = 0; i < elems.length; i++) {\n        ret += (0,domutils__WEBPACK_IMPORTED_MODULE_0__.textContent)(elems[i]);\n    }\n    return ret;\n}\nfunction parseHTML(data, context, keepScripts = typeof context === 'boolean' ? context : false) {\n    if (!data || typeof data !== 'string') {\n        return null;\n    }\n    if (typeof context === 'boolean') {\n        keepScripts = context;\n    }\n    const parsed = this.load(data, this._options, false);\n    if (!keepScripts) {\n        parsed('script').remove();\n    }\n    /*\n     * The `children` array is used by Cheerio internally to group elements that\n     * share the same parents. When nodes created through `parseHTML` are\n     * inserted into previously-existing DOM structures, they will be removed\n     * from the `children` array. The results of `parseHTML` should remain\n     * constant across these operations, so a shallow copy should be returned.\n     */\n    return [...parsed.root()[0].children];\n}\n/**\n * Sometimes you need to work with the top-level root element. To query it, you\n * can use `$.root()`.\n *\n * @category Static\n * @example\n *\n * ```js\n * $.root().append('<ul id=\"vegetables\"></ul>').html();\n * //=> <ul id=\"fruits\">...</ul><ul id=\"vegetables\"></ul>\n * ```\n *\n * @returns Cheerio instance wrapping the root node.\n * @alias Cheerio.root\n */\nfunction root() {\n    return this(this._root);\n}\n/**\n * Checks to see if the `contained` DOM element is a descendant of the\n * `container` DOM element.\n *\n * @category Static\n * @param container - Potential parent node.\n * @param contained - Potential child node.\n * @returns Indicates if the nodes contain one another.\n * @alias Cheerio.contains\n * @see {@link https://api.jquery.com/jQuery.contains/}\n */\nfunction contains(container, contained) {\n    // According to the jQuery API, an element does not \"contain\" itself\n    if (contained === container) {\n        return false;\n    }\n    /*\n     * Step up the descendants, stopping when the root element is reached\n     * (signaled by `.parent` returning a reference to the same object)\n     */\n    let next = contained;\n    while (next && next !== next.parent) {\n        next = next.parent;\n        if (next === container) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Extract multiple values from a document, and store them in an object.\n *\n * @category Static\n * @param map - An object containing key-value pairs. The keys are the names of\n *   the properties to be created on the object, and the values are the\n *   selectors to be used to extract the values.\n * @returns An object containing the extracted values.\n */\nfunction extract(map) {\n    return this.root().extract(map);\n}\n/**\n * $.merge().\n *\n * @category Static\n * @param arr1 - First array.\n * @param arr2 - Second array.\n * @returns `arr1`, with elements of `arr2` inserted.\n * @alias Cheerio.merge\n * @see {@link https://api.jquery.com/jQuery.merge/}\n */\nfunction merge(arr1, arr2) {\n    if (!isArrayLike(arr1) || !isArrayLike(arr2)) {\n        return;\n    }\n    let newLength = arr1.length;\n    const len = +arr2.length;\n    for (let i = 0; i < len; i++) {\n        arr1[newLength++] = arr2[i];\n    }\n    arr1.length = newLength;\n    return arr1;\n}\n/**\n * Checks if an object is array-like.\n *\n * @category Static\n * @param item - Item to check.\n * @returns Indicates if the item is array-like.\n */\nfunction isArrayLike(item) {\n    if (Array.isArray(item)) {\n        return true;\n    }\n    if (typeof item !== 'object' ||\n        item === null ||\n        !('length' in item) ||\n        typeof item.length !== 'number' ||\n        item.length < 0) {\n        return false;\n    }\n    for (let i = 0; i < item.length; i++) {\n        if (!(i in item)) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceMappingURL=static.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/static.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/utils.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelCase: () => (/* binding */ camelCase),\n/* harmony export */   cssCase: () => (/* binding */ cssCase),\n/* harmony export */   domEach: () => (/* binding */ domEach),\n/* harmony export */   isCheerio: () => (/* binding */ isCheerio),\n/* harmony export */   isHtml: () => (/* binding */ isHtml)\n/* harmony export */ });\n/**\n * Checks if an object is a Cheerio instance.\n *\n * @category Utils\n * @param maybeCheerio - The object to check.\n * @returns Whether the object is a Cheerio instance.\n */\nfunction isCheerio(maybeCheerio) {\n    return maybeCheerio.cheerio != null;\n}\n/**\n * Convert a string to camel case notation.\n *\n * @private\n * @category Utils\n * @param str - The string to be converted.\n * @returns String in camel case notation.\n */\nfunction camelCase(str) {\n    return str.replace(/[._-](\\w|$)/g, (_, x) => x.toUpperCase());\n}\n/**\n * Convert a string from camel case to \"CSS case\", where word boundaries are\n * described by hyphens (\"-\") and all characters are lower-case.\n *\n * @private\n * @category Utils\n * @param str - The string to be converted.\n * @returns String in \"CSS case\".\n */\nfunction cssCase(str) {\n    return str.replace(/[A-Z]/g, '-$&').toLowerCase();\n}\n/**\n * Iterate over each DOM element without creating intermediary Cheerio\n * instances.\n *\n * This is indented for use internally to avoid otherwise unnecessary memory\n * pressure introduced by _make.\n *\n * @category Utils\n * @param array - The array to iterate over.\n * @param fn - Function to call.\n * @returns The original instance.\n */\nfunction domEach(array, fn) {\n    const len = array.length;\n    for (let i = 0; i < len; i++)\n        fn(array[i], i);\n    return array;\n}\nvar CharacterCode;\n(function (CharacterCode) {\n    CharacterCode[CharacterCode[\"LowerA\"] = 97] = \"LowerA\";\n    CharacterCode[CharacterCode[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharacterCode[CharacterCode[\"UpperA\"] = 65] = \"UpperA\";\n    CharacterCode[CharacterCode[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharacterCode[CharacterCode[\"Exclamation\"] = 33] = \"Exclamation\";\n})(CharacterCode || (CharacterCode = {}));\n/**\n * Check if string is HTML.\n *\n * Tests for a `<` within a string, immediate followed by a letter and\n * eventually followed by a `>`.\n *\n * @private\n * @category Utils\n * @param str - The string to check.\n * @returns Indicates if `str` is HTML.\n */\nfunction isHtml(str) {\n    const tagStart = str.indexOf('<');\n    if (tagStart === -1 || tagStart > str.length - 3)\n        return false;\n    const tagChar = str.charCodeAt(tagStart + 1);\n    return (((tagChar >= CharacterCode.LowerA && tagChar <= CharacterCode.LowerZ) ||\n        (tagChar >= CharacterCode.UpperA && tagChar <= CharacterCode.UpperZ) ||\n        tagChar === CharacterCode.Exclamation) &&\n        str.includes('>', tagStart + 2));\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/utils.js\n");

/***/ })

};
;