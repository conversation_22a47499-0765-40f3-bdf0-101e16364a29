/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/boolbase";
exports.ids = ["vendor-chunks/boolbase"];
exports.modules = {

/***/ "(rsc)/./node_modules/boolbase/index.js":
/*!****************************************!*\
  !*** ./node_modules/boolbase/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = {\n\ttrueFunc: function trueFunc(){\n\t\treturn true;\n\t},\n\tfalseFunc: function falseFunc(){\n\t\treturn false;\n\t}\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYm9vbGJhc2UvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcYm9vbGJhc2VcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuXHR0cnVlRnVuYzogZnVuY3Rpb24gdHJ1ZUZ1bmMoKXtcblx0XHRyZXR1cm4gdHJ1ZTtcblx0fSxcblx0ZmFsc2VGdW5jOiBmdW5jdGlvbiBmYWxzZUZ1bmMoKXtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/boolbase/index.js\n");

/***/ })

};
;