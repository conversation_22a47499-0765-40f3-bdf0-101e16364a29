/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/betexplorer/match-content/route";
exports.ids = ["app/api/betexplorer/match-content/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&page=%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&page=%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_match_content_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/betexplorer/match-content/route.ts */ \"(rsc)/./src/app/api/betexplorer/match-content/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/betexplorer/match-content/route\",\n        pathname: \"/api/betexplorer/match-content\",\n        filename: \"route\",\n        bundlePath: \"app/api/betexplorer/match-content/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\api\\\\betexplorer\\\\match-content\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_match_content_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/betexplorer/match-content/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&page=%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/betexplorer/match-content/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/betexplorer/match-content/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/betexplorer-types */ \"(rsc)/./src/lib/betexplorer-types.ts\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\n\n/**\n * API Route para Match Content do BetExplorer\n * Extrai informações detalhadas de jogos específicos incluindo nomes dos times\n */ // Cache para detalhes de jogos (válido por mais tempo que live results)\nconst matchCache = new Map();\nconst MATCH_CACHE_TTL = 5 * 60 * 1000 // 5 minutos\n;\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const eventId = searchParams.get('eventId');\n        const template = searchParams.get('template') || 'new';\n        const bettingType = searchParams.get('bettingType') || '1x2';\n        const token = searchParams.get('token') // Token de sessão opcional\n        ;\n        const lang = searchParams.get('lang') || 'en';\n        if (!eventId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'eventId é obrigatório',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 400\n            });\n        }\n        // Verificar cache\n        const cached = matchCache.get(eventId);\n        const now = Date.now();\n        if (cached && now - cached.timestamp < MATCH_CACHE_TTL) {\n            console.log(`📦 Retornando dados do cache - Match Content: ${eventId}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cached.data,\n                timestamp: new Date().toISOString(),\n                cached: true\n            });\n        }\n        console.log(`🔄 Buscando dados do BetExplorer - Match Content: ${eventId}`);\n        // Construir URL da API\n        let apiUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php`;\n        const params = new URLSearchParams({\n            e: eventId,\n            t: template,\n            bt: bettingType,\n            lang: lang\n        });\n        if (token) {\n            params.append('ts', token);\n        }\n        apiUrl += `?${params.toString()}`;\n        // Fazer requisição para BetExplorer\n        const response = await fetch(apiUrl, {\n            method: 'GET',\n            headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.HEADERS,\n            signal: AbortSignal.timeout(15000) // 15 segundos para parsing HTML\n        });\n        if (!response.ok) {\n            throw new Error(`BetExplorer API retornou status ${response.status}`);\n        }\n        const htmlContent = await response.text();\n        if (!htmlContent || htmlContent.trim().length === 0) {\n            throw new Error('Resposta HTML vazia da API do BetExplorer');\n        }\n        // Fazer parsing do HTML usando Cheerio\n        const $ = cheerio__WEBPACK_IMPORTED_MODULE_2__.load(htmlContent);\n        // Extrair informações dos times\n        const teamNames = [];\n        $('h3').each((index, element)=>{\n            if (index < 2) {\n                const teamName = $(element).text().trim();\n                if (teamName) {\n                    teamNames.push(teamName);\n                }\n            }\n        });\n        // Extrair logos dos times\n        const teamLogos = [];\n        $('img[src*=\"/team-logo/\"]').each((index, element)=>{\n            if (index < 2) {\n                const logoSrc = $(element).attr('src');\n                if (logoSrc) {\n                    // Converter para URL absoluta se necessário\n                    const fullLogoUrl = logoSrc.startsWith('http') ? logoSrc : `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.BASE_URL}${logoSrc}`;\n                    teamLogos.push(fullLogoUrl);\n                }\n            }\n        });\n        // Extrair informações da competição\n        let tournament = '';\n        let country = '';\n        // Buscar por padrões conhecidos no HTML\n        const htmlText = $.text();\n        // Padrões comuns de competições\n        const competitionPatterns = [\n            /Champions League/i,\n            /Premier League/i,\n            /La Liga/i,\n            /Serie A/i,\n            /Bundesliga/i,\n            /Ligue 1/i,\n            /Copa do Mundo/i,\n            /Eurocopa/i,\n            /Copa América/i\n        ];\n        for (const pattern of competitionPatterns){\n            const match = htmlText.match(pattern);\n            if (match) {\n                tournament = match[0];\n                break;\n            }\n        }\n        // Tentar extrair país (simplificado)\n        const countryPatterns = [\n            /England/i,\n            /Spain/i,\n            /Italy/i,\n            /Germany/i,\n            /France/i,\n            /Brazil/i,\n            /Argentina/i\n        ];\n        for (const pattern of countryPatterns){\n            const match = htmlText.match(pattern);\n            if (match) {\n                country = match[0];\n                break;\n            }\n        }\n        // Validar dados extraídos\n        if (teamNames.length < 2) {\n            throw new Error('Não foi possível extrair nomes dos times do HTML');\n        }\n        // Construir objeto de resposta\n        const matchDetails = {\n            event_id: eventId,\n            homeTeam: teamNames[0],\n            awayTeam: teamNames[1],\n            homeTeamLogo: teamLogos[0] || undefined,\n            awayTeamLogo: teamLogos[1] || undefined,\n            tournament: tournament || 'Competição não identificada',\n            country: country || 'País não identificado'\n        };\n        // Salvar no cache\n        matchCache.set(eventId, {\n            data: matchDetails,\n            timestamp: now\n        });\n        console.log(`✅ Match Content processado: ${matchDetails.homeTeam} x ${matchDetails.awayTeam}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: matchDetails,\n            timestamp: new Date().toISOString(),\n            cached: false\n        });\n    } catch (error) {\n        console.error('❌ Erro na API Match Content:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Erro desconhecido',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n// Limpar cache periodicamente (executar a cada 10 minutos)\nsetInterval(()=>{\n    const now = Date.now();\n    for (const [eventId, cached] of matchCache.entries()){\n        if (now - cached.timestamp > MATCH_CACHE_TTL) {\n            matchCache.delete(eventId);\n        }\n    }\n}, 10 * 60 * 1000);\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type'\n        }\n    });\n}\nconst runtime = 'nodejs';\nconst dynamic = 'force-dynamic';\nconst revalidate = 0;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/betexplorer/match-content/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/betexplorer-types.ts":
/*!**************************************!*\
  !*** ./src/lib/betexplorer-types.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BETEXPLORER_CONFIG: () => (/* binding */ BETEXPLORER_CONFIG),\n/* harmony export */   MARKET_TYPES: () => (/* binding */ MARKET_TYPES),\n/* harmony export */   SPORT_IDS: () => (/* binding */ SPORT_IDS)\n/* harmony export */ });\n/**\n * Tipos TypeScript para as APIs do BetExplorer\n * Baseado na documentação completa das APIs descobertas\n */ // ============================================================================\n// TIPOS BASE\n// ============================================================================\n// ============================================================================\n// CONFIGURAÇÕES E CONSTANTES\n// ============================================================================\nconst BETEXPLORER_CONFIG = {\n    BASE_URL: 'https://www.betexplorer.com',\n    RATE_LIMIT_MS: 2000,\n    CACHE_TTL_MS: 30000,\n    HEADERS: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Accept': 'application/json, text/html, */*',\n        'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Referer': 'https://www.betexplorer.com'\n    }\n};\nconst SPORT_IDS = {\n    FOOTBALL: 1,\n    TENNIS: 2,\n    BASKETBALL: 3,\n    HOCKEY: 4\n};\nconst MARKET_TYPES = {\n    ODDS_1X2: '1x2',\n    OVER_UNDER: 'ou',\n    ASIAN_HANDICAP: 'ah',\n    DRAW_NO_BET: 'dnb',\n    DOUBLE_CHANCE: 'dc',\n    BOTH_TEAMS_TO_SCORE: 'btts'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/betexplorer-types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&page=%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Fmatch-content%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();