"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-encoding";
exports.ids = ["vendor-chunks/whatwg-encoding"];
exports.modules = {

/***/ "(rsc)/./node_modules/whatwg-encoding/lib/labels-to-names.json":
/*!***************************************************************!*\
  !*** ./node_modules/whatwg-encoding/lib/labels-to-names.json ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"866":"IBM866","unicode-1-1-utf-8":"UTF-8","unicode11utf8":"UTF-8","unicode20utf8":"UTF-8","utf-8":"UTF-8","utf8":"UTF-8","x-unicode20utf8":"UTF-8","cp866":"IBM866","csibm866":"IBM866","ibm866":"IBM866","csisolatin2":"ISO-8859-2","iso-8859-2":"ISO-8859-2","iso-ir-101":"ISO-8859-2","iso8859-2":"ISO-8859-2","iso88592":"ISO-8859-2","iso_8859-2":"ISO-8859-2","iso_8859-2:1987":"ISO-8859-2","l2":"ISO-8859-2","latin2":"ISO-8859-2","csisolatin3":"ISO-8859-3","iso-8859-3":"ISO-8859-3","iso-ir-109":"ISO-8859-3","iso8859-3":"ISO-8859-3","iso88593":"ISO-8859-3","iso_8859-3":"ISO-8859-3","iso_8859-3:1988":"ISO-8859-3","l3":"ISO-8859-3","latin3":"ISO-8859-3","csisolatin4":"ISO-8859-4","iso-8859-4":"ISO-8859-4","iso-ir-110":"ISO-8859-4","iso8859-4":"ISO-8859-4","iso88594":"ISO-8859-4","iso_8859-4":"ISO-8859-4","iso_8859-4:1988":"ISO-8859-4","l4":"ISO-8859-4","latin4":"ISO-8859-4","csisolatincyrillic":"ISO-8859-5","cyrillic":"ISO-8859-5","iso-8859-5":"ISO-8859-5","iso-ir-144":"ISO-8859-5","iso8859-5":"ISO-8859-5","iso88595":"ISO-8859-5","iso_8859-5":"ISO-8859-5","iso_8859-5:1988":"ISO-8859-5","arabic":"ISO-8859-6","asmo-708":"ISO-8859-6","csiso88596e":"ISO-8859-6","csiso88596i":"ISO-8859-6","csisolatinarabic":"ISO-8859-6","ecma-114":"ISO-8859-6","iso-8859-6":"ISO-8859-6","iso-8859-6-e":"ISO-8859-6","iso-8859-6-i":"ISO-8859-6","iso-ir-127":"ISO-8859-6","iso8859-6":"ISO-8859-6","iso88596":"ISO-8859-6","iso_8859-6":"ISO-8859-6","iso_8859-6:1987":"ISO-8859-6","csisolatingreek":"ISO-8859-7","ecma-118":"ISO-8859-7","elot_928":"ISO-8859-7","greek":"ISO-8859-7","greek8":"ISO-8859-7","iso-8859-7":"ISO-8859-7","iso-ir-126":"ISO-8859-7","iso8859-7":"ISO-8859-7","iso88597":"ISO-8859-7","iso_8859-7":"ISO-8859-7","iso_8859-7:1987":"ISO-8859-7","sun_eu_greek":"ISO-8859-7","csiso88598e":"ISO-8859-8","csisolatinhebrew":"ISO-8859-8","hebrew":"ISO-8859-8","iso-8859-8":"ISO-8859-8","iso-8859-8-e":"ISO-8859-8","iso-ir-138":"ISO-8859-8","iso8859-8":"ISO-8859-8","iso88598":"ISO-8859-8","iso_8859-8":"ISO-8859-8","iso_8859-8:1988":"ISO-8859-8","visual":"ISO-8859-8","csisolatin6":"ISO-8859-10","iso-8859-10":"ISO-8859-10","iso-ir-157":"ISO-8859-10","iso8859-10":"ISO-8859-10","iso885910":"ISO-8859-10","l6":"ISO-8859-10","latin6":"ISO-8859-10","iso-8859-13":"ISO-8859-13","iso8859-13":"ISO-8859-13","iso885913":"ISO-8859-13","iso-8859-14":"ISO-8859-14","iso8859-14":"ISO-8859-14","iso885914":"ISO-8859-14","csisolatin9":"ISO-8859-15","iso-8859-15":"ISO-8859-15","iso8859-15":"ISO-8859-15","iso885915":"ISO-8859-15","iso_8859-15":"ISO-8859-15","l9":"ISO-8859-15","iso-8859-16":"ISO-8859-16","cskoi8r":"KOI8-R","koi":"KOI8-R","koi8":"KOI8-R","koi8-r":"KOI8-R","koi8_r":"KOI8-R","koi8-ru":"KOI8-U","koi8-u":"KOI8-U","csmacintosh":"macintosh","mac":"macintosh","macintosh":"macintosh","x-mac-roman":"macintosh","dos-874":"windows-874","iso-8859-11":"windows-874","iso8859-11":"windows-874","iso885911":"windows-874","tis-620":"windows-874","windows-874":"windows-874","cp1250":"windows-1250","windows-1250":"windows-1250","x-cp1250":"windows-1250","cp1251":"windows-1251","windows-1251":"windows-1251","x-cp1251":"windows-1251","ansi_x3.4-1968":"windows-1252","ascii":"windows-1252","cp1252":"windows-1252","cp819":"windows-1252","csisolatin1":"windows-1252","ibm819":"windows-1252","iso-8859-1":"windows-1252","iso-ir-100":"windows-1252","iso8859-1":"windows-1252","iso88591":"windows-1252","iso_8859-1":"windows-1252","iso_8859-1:1987":"windows-1252","l1":"windows-1252","latin1":"windows-1252","us-ascii":"windows-1252","windows-1252":"windows-1252","x-cp1252":"windows-1252","cp1253":"windows-1253","windows-1253":"windows-1253","x-cp1253":"windows-1253","cp1254":"windows-1254","csisolatin5":"windows-1254","iso-8859-9":"windows-1254","iso-ir-148":"windows-1254","iso8859-9":"windows-1254","iso88599":"windows-1254","iso_8859-9":"windows-1254","iso_8859-9:1989":"windows-1254","l5":"windows-1254","latin5":"windows-1254","windows-1254":"windows-1254","x-cp1254":"windows-1254","cp1255":"windows-1255","windows-1255":"windows-1255","x-cp1255":"windows-1255","cp1256":"windows-1256","windows-1256":"windows-1256","x-cp1256":"windows-1256","cp1257":"windows-1257","windows-1257":"windows-1257","x-cp1257":"windows-1257","cp1258":"windows-1258","windows-1258":"windows-1258","x-cp1258":"windows-1258","chinese":"GBK","csgb2312":"GBK","csiso58gb231280":"GBK","gb2312":"GBK","gb_2312":"GBK","gb_2312-80":"GBK","gbk":"GBK","iso-ir-58":"GBK","x-gbk":"GBK","gb18030":"gb18030","big5":"Big5","big5-hkscs":"Big5","cn-big5":"Big5","csbig5":"Big5","x-x-big5":"Big5","cseucpkdfmtjapanese":"EUC-JP","euc-jp":"EUC-JP","x-euc-jp":"EUC-JP","csshiftjis":"Shift_JIS","ms932":"Shift_JIS","ms_kanji":"Shift_JIS","shift-jis":"Shift_JIS","shift_jis":"Shift_JIS","sjis":"Shift_JIS","windows-31j":"Shift_JIS","x-sjis":"Shift_JIS","cseuckr":"EUC-KR","csksc56011987":"EUC-KR","euc-kr":"EUC-KR","iso-ir-149":"EUC-KR","korean":"EUC-KR","ks_c_5601-1987":"EUC-KR","ks_c_5601-1989":"EUC-KR","ksc5601":"EUC-KR","ksc_5601":"EUC-KR","windows-949":"EUC-KR","unicodefffe":"UTF-16BE","utf-16be":"UTF-16BE","csunicode":"UTF-16LE","iso-10646-ucs-2":"UTF-16LE","ucs-2":"UTF-16LE","unicode":"UTF-16LE","unicodefeff":"UTF-16LE","utf-16":"UTF-16LE","utf-16le":"UTF-16LE","x-user-defined":"x-user-defined"}');

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-encoding/lib/supported-names.json":
/*!***************************************************************!*\
  !*** ./node_modules/whatwg-encoding/lib/supported-names.json ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('["UTF-8","IBM866","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-10","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","KOI8-R","KOI8-U","macintosh","windows-874","windows-1250","windows-1251","windows-1252","windows-1253","windows-1254","windows-1255","windows-1256","windows-1257","windows-1258","GBK","gb18030","Big5","EUC-JP","Shift_JIS","EUC-KR","UTF-16BE","UTF-16LE","x-user-defined"]');

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js":
/*!*************************************************************!*\
  !*** ./node_modules/whatwg-encoding/lib/whatwg-encoding.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst iconvLite = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\nconst supportedNames = __webpack_require__(/*! ./supported-names.json */ \"(rsc)/./node_modules/whatwg-encoding/lib/supported-names.json\");\nconst labelsToNames = __webpack_require__(/*! ./labels-to-names.json */ \"(rsc)/./node_modules/whatwg-encoding/lib/labels-to-names.json\");\n\nconst supportedNamesSet = new Set(supportedNames);\n\n// https://encoding.spec.whatwg.org/#concept-encoding-get\nexports.labelToName = label => {\n  label = String(label).trim().toLowerCase();\n\n  return labelsToNames[label] || null;\n};\n\n// https://encoding.spec.whatwg.org/#decode\nexports.decode = (uint8Array, fallbackEncodingName) => {\n  let encoding = fallbackEncodingName;\n  if (!exports.isSupported(encoding)) {\n    throw new RangeError(`\"${encoding}\" is not a supported encoding name`);\n  }\n\n  const bomEncoding = exports.getBOMEncoding(uint8Array);\n  if (bomEncoding !== null) {\n    encoding = bomEncoding;\n    // iconv-lite will strip BOMs for us, so no need to do the extra byte removal that the spec does.\n    // Note that we won't end up in the x-user-defined case when there's a bomEncoding.\n  }\n\n  if (encoding === \"x-user-defined\") {\n    // https://encoding.spec.whatwg.org/#x-user-defined-decoder\n    let result = \"\";\n    for (const byte of uint8Array) {\n      if (byte <= 0x7F) {\n        result += String.fromCodePoint(byte);\n      } else {\n        result += String.fromCodePoint(0xF780 + byte - 0x80);\n      }\n    }\n    return result;\n  }\n\n  return iconvLite.decode(uint8Array, encoding);\n};\n\n// https://github.com/whatwg/html/issues/1910#issuecomment-254017369\nexports.getBOMEncoding = uint8Array => {\n  if (uint8Array[0] === 0xFE && uint8Array[1] === 0xFF) {\n    return \"UTF-16BE\";\n  } else if (uint8Array[0] === 0xFF && uint8Array[1] === 0xFE) {\n    return \"UTF-16LE\";\n  } else if (uint8Array[0] === 0xEF && uint8Array[1] === 0xBB && uint8Array[2] === 0xBF) {\n    return \"UTF-8\";\n  }\n\n  return null;\n};\n\nexports.isSupported = name => {\n  return supportedNamesSet.has(String(name));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js\n");

/***/ })

};
;