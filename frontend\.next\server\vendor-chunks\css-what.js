"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-what";
exports.ids = ["vendor-chunks/css-what"];
exports.modules = {

/***/ "(rsc)/./node_modules/css-what/lib/es/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/parse.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isTraversal: () => (/* binding */ isTraversal),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/css-what/lib/es/types.js\");\n\nconst reName = /^[^\\\\#]?(?:\\\\(?:[\\da-f]{1,6}\\s?|.)|[\\w\\-\\u00b0-\\uFFFF])+/;\nconst reEscape = /\\\\([\\da-f]{1,6}\\s?|(\\s)|.)/gi;\nconst actionTypes = new Map([\n    [126 /* Tilde */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element],\n    [94 /* Circumflex */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Start],\n    [36 /* Dollar */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.End],\n    [42 /* Asterisk */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Any],\n    [33 /* ExclamationMark */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Not],\n    [124 /* Pipe */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Hyphen],\n]);\n// Pseudos, whose data property is parsed as well.\nconst unpackPseudos = new Set([\n    \"has\",\n    \"not\",\n    \"matches\",\n    \"is\",\n    \"where\",\n    \"host\",\n    \"host-context\",\n]);\n/**\n * Checks whether a specific selector is a traversal.\n * This is useful eg. in swapping the order of elements that\n * are not traversals.\n *\n * @param selector Selector to check.\n */\nfunction isTraversal(selector) {\n    switch (selector.type) {\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator:\n            return true;\n        default:\n            return false;\n    }\n}\nconst stripQuotesFromPseudos = new Set([\"contains\", \"icontains\"]);\n// Unescape function taken from https://github.com/jquery/sizzle/blob/master/src/sizzle.js#L152\nfunction funescape(_, escaped, escapedWhitespace) {\n    const high = parseInt(escaped, 16) - 0x10000;\n    // NaN means non-codepoint\n    return high !== high || escapedWhitespace\n        ? escaped\n        : high < 0\n            ? // BMP codepoint\n                String.fromCharCode(high + 0x10000)\n            : // Supplemental Plane codepoint (surrogate pair)\n                String.fromCharCode((high >> 10) | 0xd800, (high & 0x3ff) | 0xdc00);\n}\nfunction unescapeCSS(str) {\n    return str.replace(reEscape, funescape);\n}\nfunction isQuote(c) {\n    return c === 39 /* SingleQuote */ || c === 34 /* DoubleQuote */;\n}\nfunction isWhitespace(c) {\n    return (c === 32 /* Space */ ||\n        c === 9 /* Tab */ ||\n        c === 10 /* NewLine */ ||\n        c === 12 /* FormFeed */ ||\n        c === 13 /* CarriageReturn */);\n}\n/**\n * Parses `selector`, optionally with the passed `options`.\n *\n * @param selector Selector to parse.\n * @param options Options for parsing.\n * @returns Returns a two-dimensional array.\n * The first dimension represents selectors separated by commas (eg. `sub1, sub2`),\n * the second contains the relevant tokens for that selector.\n */\nfunction parse(selector) {\n    const subselects = [];\n    const endIndex = parseSelector(subselects, `${selector}`, 0);\n    if (endIndex < selector.length) {\n        throw new Error(`Unmatched selector: ${selector.slice(endIndex)}`);\n    }\n    return subselects;\n}\nfunction parseSelector(subselects, selector, selectorIndex) {\n    let tokens = [];\n    function getName(offset) {\n        const match = selector.slice(selectorIndex + offset).match(reName);\n        if (!match) {\n            throw new Error(`Expected name, found ${selector.slice(selectorIndex)}`);\n        }\n        const [name] = match;\n        selectorIndex += offset + name.length;\n        return unescapeCSS(name);\n    }\n    function stripWhitespace(offset) {\n        selectorIndex += offset;\n        while (selectorIndex < selector.length &&\n            isWhitespace(selector.charCodeAt(selectorIndex))) {\n            selectorIndex++;\n        }\n    }\n    function readValueWithParenthesis() {\n        selectorIndex += 1;\n        const start = selectorIndex;\n        let counter = 1;\n        for (; counter > 0 && selectorIndex < selector.length; selectorIndex++) {\n            if (selector.charCodeAt(selectorIndex) ===\n                40 /* LeftParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter++;\n            }\n            else if (selector.charCodeAt(selectorIndex) ===\n                41 /* RightParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter--;\n            }\n        }\n        if (counter) {\n            throw new Error(\"Parenthesis not matched\");\n        }\n        return unescapeCSS(selector.slice(start, selectorIndex - 1));\n    }\n    function isEscaped(pos) {\n        let slashCount = 0;\n        while (selector.charCodeAt(--pos) === 92 /* BackSlash */)\n            slashCount++;\n        return (slashCount & 1) === 1;\n    }\n    function ensureNotTraversal() {\n        if (tokens.length > 0 && isTraversal(tokens[tokens.length - 1])) {\n            throw new Error(\"Did not expect successive traversals.\");\n        }\n    }\n    function addTraversal(type) {\n        if (tokens.length > 0 &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens[tokens.length - 1].type = type;\n            return;\n        }\n        ensureNotTraversal();\n        tokens.push({ type });\n    }\n    function addSpecialAttribute(name, action) {\n        tokens.push({\n            type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n            name,\n            action,\n            value: getName(1),\n            namespace: null,\n            ignoreCase: \"quirks\",\n        });\n    }\n    /**\n     * We have finished parsing the current part of the selector.\n     *\n     * Remove descendant tokens at the end if they exist,\n     * and return the last index, so that parsing can be\n     * picked up from here.\n     */\n    function finalizeSubselector() {\n        if (tokens.length &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens.pop();\n        }\n        if (tokens.length === 0) {\n            throw new Error(\"Empty sub-selector\");\n        }\n        subselects.push(tokens);\n    }\n    stripWhitespace(0);\n    if (selector.length === selectorIndex) {\n        return selectorIndex;\n    }\n    loop: while (selectorIndex < selector.length) {\n        const firstChar = selector.charCodeAt(selectorIndex);\n        switch (firstChar) {\n            // Whitespace\n            case 32 /* Space */:\n            case 9 /* Tab */:\n            case 10 /* NewLine */:\n            case 12 /* FormFeed */:\n            case 13 /* CarriageReturn */: {\n                if (tokens.length === 0 ||\n                    tokens[0].type !== _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n                    ensureNotTraversal();\n                    tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant });\n                }\n                stripWhitespace(1);\n                break;\n            }\n            // Traversals\n            case 62 /* GreaterThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child);\n                stripWhitespace(1);\n                break;\n            }\n            case 60 /* LessThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent);\n                stripWhitespace(1);\n                break;\n            }\n            case 126 /* Tilde */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling);\n                stripWhitespace(1);\n                break;\n            }\n            case 43 /* Plus */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent);\n                stripWhitespace(1);\n                break;\n            }\n            // Special attribute selectors: .class, #id\n            case 46 /* Period */: {\n                addSpecialAttribute(\"class\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element);\n                break;\n            }\n            case 35 /* Hash */: {\n                addSpecialAttribute(\"id\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals);\n                break;\n            }\n            case 91 /* LeftSquareBracket */: {\n                stripWhitespace(1);\n                // Determine attribute name and namespace\n                let name;\n                let namespace = null;\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */) {\n                    // Equivalent to no namespace\n                    name = getName(1);\n                }\n                else if (selector.startsWith(\"*|\", selectorIndex)) {\n                    namespace = \"*\";\n                    name = getName(2);\n                }\n                else {\n                    name = getName(0);\n                    if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                        selector.charCodeAt(selectorIndex + 1) !==\n                            61 /* Equal */) {\n                        namespace = name;\n                        name = getName(1);\n                    }\n                }\n                stripWhitespace(0);\n                // Determine comparison operation\n                let action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Exists;\n                const possibleAction = actionTypes.get(selector.charCodeAt(selectorIndex));\n                if (possibleAction) {\n                    action = possibleAction;\n                    if (selector.charCodeAt(selectorIndex + 1) !==\n                        61 /* Equal */) {\n                        throw new Error(\"Expected `=`\");\n                    }\n                    stripWhitespace(2);\n                }\n                else if (selector.charCodeAt(selectorIndex) === 61 /* Equal */) {\n                    action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals;\n                    stripWhitespace(1);\n                }\n                // Determine value\n                let value = \"\";\n                let ignoreCase = null;\n                if (action !== \"exists\") {\n                    if (isQuote(selector.charCodeAt(selectorIndex))) {\n                        const quote = selector.charCodeAt(selectorIndex);\n                        let sectionEnd = selectorIndex + 1;\n                        while (sectionEnd < selector.length &&\n                            (selector.charCodeAt(sectionEnd) !== quote ||\n                                isEscaped(sectionEnd))) {\n                            sectionEnd += 1;\n                        }\n                        if (selector.charCodeAt(sectionEnd) !== quote) {\n                            throw new Error(\"Attribute value didn't end\");\n                        }\n                        value = unescapeCSS(selector.slice(selectorIndex + 1, sectionEnd));\n                        selectorIndex = sectionEnd + 1;\n                    }\n                    else {\n                        const valueStart = selectorIndex;\n                        while (selectorIndex < selector.length &&\n                            ((!isWhitespace(selector.charCodeAt(selectorIndex)) &&\n                                selector.charCodeAt(selectorIndex) !==\n                                    93 /* RightSquareBracket */) ||\n                                isEscaped(selectorIndex))) {\n                            selectorIndex += 1;\n                        }\n                        value = unescapeCSS(selector.slice(valueStart, selectorIndex));\n                    }\n                    stripWhitespace(0);\n                    // See if we have a force ignore flag\n                    const forceIgnore = selector.charCodeAt(selectorIndex) | 0x20;\n                    // If the forceIgnore flag is set (either `i` or `s`), use that value\n                    if (forceIgnore === 115 /* LowerS */) {\n                        ignoreCase = false;\n                        stripWhitespace(1);\n                    }\n                    else if (forceIgnore === 105 /* LowerI */) {\n                        ignoreCase = true;\n                        stripWhitespace(1);\n                    }\n                }\n                if (selector.charCodeAt(selectorIndex) !==\n                    93 /* RightSquareBracket */) {\n                    throw new Error(\"Attribute selector didn't terminate\");\n                }\n                selectorIndex += 1;\n                const attributeSelector = {\n                    type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n                    name,\n                    action,\n                    value,\n                    namespace,\n                    ignoreCase,\n                };\n                tokens.push(attributeSelector);\n                break;\n            }\n            case 58 /* Colon */: {\n                if (selector.charCodeAt(selectorIndex + 1) === 58 /* Colon */) {\n                    tokens.push({\n                        type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.PseudoElement,\n                        name: getName(2).toLowerCase(),\n                        data: selector.charCodeAt(selectorIndex) ===\n                            40 /* LeftParenthesis */\n                            ? readValueWithParenthesis()\n                            : null,\n                    });\n                    continue;\n                }\n                const name = getName(1).toLowerCase();\n                let data = null;\n                if (selector.charCodeAt(selectorIndex) ===\n                    40 /* LeftParenthesis */) {\n                    if (unpackPseudos.has(name)) {\n                        if (isQuote(selector.charCodeAt(selectorIndex + 1))) {\n                            throw new Error(`Pseudo-selector ${name} cannot be quoted`);\n                        }\n                        data = [];\n                        selectorIndex = parseSelector(data, selector, selectorIndex + 1);\n                        if (selector.charCodeAt(selectorIndex) !==\n                            41 /* RightParenthesis */) {\n                            throw new Error(`Missing closing parenthesis in :${name} (${selector})`);\n                        }\n                        selectorIndex += 1;\n                    }\n                    else {\n                        data = readValueWithParenthesis();\n                        if (stripQuotesFromPseudos.has(name)) {\n                            const quot = data.charCodeAt(0);\n                            if (quot === data.charCodeAt(data.length - 1) &&\n                                isQuote(quot)) {\n                                data = data.slice(1, -1);\n                            }\n                        }\n                        data = unescapeCSS(data);\n                    }\n                }\n                tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Pseudo, name, data });\n                break;\n            }\n            case 44 /* Comma */: {\n                finalizeSubselector();\n                tokens = [];\n                stripWhitespace(1);\n                break;\n            }\n            default: {\n                if (selector.startsWith(\"/*\", selectorIndex)) {\n                    const endIndex = selector.indexOf(\"*/\", selectorIndex + 2);\n                    if (endIndex < 0) {\n                        throw new Error(\"Comment was not terminated\");\n                    }\n                    selectorIndex = endIndex + 2;\n                    // Remove leading whitespace\n                    if (tokens.length === 0) {\n                        stripWhitespace(0);\n                    }\n                    break;\n                }\n                let namespace = null;\n                let name;\n                if (firstChar === 42 /* Asterisk */) {\n                    selectorIndex += 1;\n                    name = \"*\";\n                }\n                else if (firstChar === 124 /* Pipe */) {\n                    name = \"\";\n                    if (selector.charCodeAt(selectorIndex + 1) === 124 /* Pipe */) {\n                        addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator);\n                        stripWhitespace(2);\n                        break;\n                    }\n                }\n                else if (reName.test(selector.slice(selectorIndex))) {\n                    name = getName(0);\n                }\n                else {\n                    break loop;\n                }\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                    selector.charCodeAt(selectorIndex + 1) !== 124 /* Pipe */) {\n                    namespace = name;\n                    if (selector.charCodeAt(selectorIndex + 1) ===\n                        42 /* Asterisk */) {\n                        name = \"*\";\n                        selectorIndex += 2;\n                    }\n                    else {\n                        name = getName(1);\n                    }\n                }\n                tokens.push(name === \"*\"\n                    ? { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Universal, namespace }\n                    : { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Tag, name, namespace });\n            }\n        }\n    }\n    finalizeSubselector();\n    return selectorIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/css-what/lib/es/types.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributeAction: () => (/* binding */ AttributeAction),\n/* harmony export */   IgnoreCaseMode: () => (/* binding */ IgnoreCaseMode),\n/* harmony export */   SelectorType: () => (/* binding */ SelectorType)\n/* harmony export */ });\nvar SelectorType;\n(function (SelectorType) {\n    SelectorType[\"Attribute\"] = \"attribute\";\n    SelectorType[\"Pseudo\"] = \"pseudo\";\n    SelectorType[\"PseudoElement\"] = \"pseudo-element\";\n    SelectorType[\"Tag\"] = \"tag\";\n    SelectorType[\"Universal\"] = \"universal\";\n    // Traversals\n    SelectorType[\"Adjacent\"] = \"adjacent\";\n    SelectorType[\"Child\"] = \"child\";\n    SelectorType[\"Descendant\"] = \"descendant\";\n    SelectorType[\"Parent\"] = \"parent\";\n    SelectorType[\"Sibling\"] = \"sibling\";\n    SelectorType[\"ColumnCombinator\"] = \"column-combinator\";\n})(SelectorType || (SelectorType = {}));\n/**\n * Modes for ignore case.\n *\n * This could be updated to an enum, and the object is\n * the current stand-in that will allow code to be updated\n * without big changes.\n */\nconst IgnoreCaseMode = {\n    Unknown: null,\n    QuirksMode: \"quirks\",\n    IgnoreCase: true,\n    CaseSensitive: false,\n};\nvar AttributeAction;\n(function (AttributeAction) {\n    AttributeAction[\"Any\"] = \"any\";\n    AttributeAction[\"Element\"] = \"element\";\n    AttributeAction[\"End\"] = \"end\";\n    AttributeAction[\"Equals\"] = \"equals\";\n    AttributeAction[\"Exists\"] = \"exists\";\n    AttributeAction[\"Hyphen\"] = \"hyphen\";\n    AttributeAction[\"Not\"] = \"not\";\n    AttributeAction[\"Start\"] = \"start\";\n})(AttributeAction || (AttributeAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/types.js\n");

/***/ })

};
;