"""
DOCUMENTAÇÃO COMPLETA DAS APIs DO BETEXPLORER
Sistema para documentar e atualizar descobertas de APIs automaticamente.
"""

import json
from datetime import datetime
from typing import Dict, Any, List


class BetExplorerAPIDocumentation:
    """Documentação completa das APIs do BetExplorer."""
    
    def __init__(self):
        """Inicializa a documentação."""
        self.documentation = {
            "project_info": {
                "name": "BetExplorer API Documentation",
                "description": "Documentação completa das APIs descobertas do BetExplorer",
                "created_at": "2025-07-25T00:00:00Z",
                "last_updated": datetime.utcnow().isoformat() + "Z",
                "version": "1.0.0",
                "total_apis": 0,
                "discovery_status": "Em andamento"
            },
            "base_info": {
                "base_url": "https://www.betexplorer.com",
                "authentication": "Não requerida",
                "rate_limiting": "Recomendado 1-2s entre requisições",
                "user_agent": "Obrigatório (usar fake-useragent)",
                "headers_required": [
                    "User-Agent",
                    "Accept",
                    "Accept-Language", 
                    "X-Requested-With: XMLHttpRequest",
                    "Referer: https://www.betexplorer.com"
                ]
            },
            "apis": {}
        }
    
    def add_api_documentation(self, api_id: str, api_data: Dict[str, Any]) -> None:
        """
        Adiciona documentação de uma API.
        
        Args:
            api_id: ID único da API
            api_data: Dados completos da API
        """
        self.documentation["apis"][api_id] = {
            **api_data,
            "documented_at": datetime.utcnow().isoformat() + "Z"
        }
        
        # Atualizar contadores
        self.documentation["project_info"]["total_apis"] = len(self.documentation["apis"])
        self.documentation["project_info"]["last_updated"] = datetime.utcnow().isoformat() + "Z"
    
    def generate_complete_documentation(self) -> Dict[str, Any]:
        """Gera documentação completa com todas as descobertas."""
        
        # 1. API LIVE RESULTS
        self.add_api_documentation("live_results", {
            "name": "Live Results API",
            "category": "Dados em Tempo Real",
            "priority": "CRÍTICA",
            "endpoint": "/gres/ajax/live-results.php",
            "method": "GET",
            "description": "API principal que retorna todos os eventos esportivos ao vivo com odds em tempo real",
            "url_example": "https://www.betexplorer.com/gres/ajax/live-results.php",
            "parameters": {
                "required": [],
                "optional": [
                    {
                        "name": "GETeventId",
                        "type": "string",
                        "description": "Filtrar por Event ID específico",
                        "example": "Yecjgyt1"
                    }
                ]
            },
            "response": {
                "format": "JSON",
                "size_typical": "200-500 KB",
                "content_type": "application/json"
            },
            "data_returned": {
                "events": {
                    "description": "Lista de todos os eventos ao vivo",
                    "structure": {
                        "event_id": "ID único do evento",
                        "score": "Placar atual (ex: '2:1')",
                        "minute": "Minuto do jogo ou 0",
                        "finished": "0=ao vivo, 1=finalizado",
                        "sport_id": "ID do esporte (1=futebol, 2=tênis, etc.)",
                        "event_stage_id": "ID do estágio do jogo"
                    }
                },
                "event_stages": {
                    "description": "Informações dos estágios dos jogos",
                    "structure": {
                        "stage_id": ["Nome do estágio", "Abreviação"]
                    }
                },
                "odds": {
                    "live_odds_1x2": "Odds 1X2 de múltiplas casas",
                    "live_odds_ou": "Odds Over/Under",
                    "live_odds_dc": "Odds Double Chance",
                    "live_odds_dnb": "Odds Draw No Bet",
                    "live_odds_btts": "Odds Both Teams To Score",
                    "live_odds_ah": "Odds Asian Handicap"
                },
                "bookmakers": "Lista de casas de apostas"
            },
            "use_cases": [
                "Monitoramento de jogos ao vivo",
                "Coleta de odds em tempo real",
                "Identificação de eventos por esporte",
                "Tracking de placares e tempos"
            ],
            "limitations": [
                "Não inclui nomes dos times",
                "Não inclui informações da competição",
                "Não inclui estatísticas detalhadas do jogo"
            ],
            "test_results": {
                "last_tested": "2025-07-25T00:00:00Z",
                "status": "FUNCIONANDO",
                "response_time_avg": "0.7s",
                "success_rate": "100%",
                "typical_events_count": "25-35"
            },
            "code_example": '''
# Exemplo de uso
import aiohttp
import json

async def get_live_results():
    url = "https://www.betexplorer.com/gres/ajax/live-results.php"
    headers = {
        'User-Agent': 'Mozilla/5.0...',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    async with aiohttp.ClientSession(headers=headers) as session:
        async with session.get(url) as response:
            data = await response.json()
            events = data.get('events', {})
            return events
            '''
        })
        
        # 2. API MATCH CONTENT
        self.add_api_documentation("match_content", {
            "name": "Match Content API",
            "category": "Detalhes dos Jogos",
            "priority": "ALTA",
            "endpoint": "/gres/ajax/match-content.php",
            "method": "GET",
            "description": "Retorna informações detalhadas de um jogo específico incluindo nomes dos times",
            "url_example": "https://www.betexplorer.com/gres/ajax/match-content.php?e=lU0lGQ56&t=new&ts=vF2C38ii&bt=1x2&lang=en",
            "parameters": {
                "required": [
                    {
                        "name": "e",
                        "type": "string",
                        "description": "Event ID do jogo",
                        "example": "lU0lGQ56"
                    },
                    {
                        "name": "t",
                        "type": "string",
                        "description": "Tipo de template",
                        "example": "new"
                    },
                    {
                        "name": "bt",
                        "type": "string",
                        "description": "Betting type",
                        "example": "1x2"
                    }
                ],
                "optional": [
                    {
                        "name": "ts",
                        "type": "string",
                        "description": "Token de sessão (8 chars alfanuméricos)",
                        "example": "vF2C38ii",
                        "note": "Opcional mas pode retornar mais dados"
                    },
                    {
                        "name": "lang",
                        "type": "string",
                        "description": "Idioma",
                        "example": "en"
                    }
                ]
            },
            "response": {
                "format": "HTML",
                "size_typical": "10-35 KB",
                "content_type": "text/html"
            },
            "data_returned": {
                "team_names": "Nomes completos dos times (em tags <h3>)",
                "team_logos": "URLs dos logos dos times",
                "tournament_info": "Nome da competição",
                "country": "País da competição",
                "recent_results": "Resultados recentes dos times",
                "head_to_head": "Alguns dados de confrontos diretos"
            },
            "parsing_method": "BeautifulSoup HTML parsing",
            "extraction_patterns": {
                "team_names": "soup.find_all('h3') - primeiros 2 elementos",
                "logos": "img tags com src contendo '/team-logo/'",
                "competition": "Busca por strings conhecidas no HTML"
            },
            "use_cases": [
                "Obter nomes dos times para eventos",
                "Identificar competições",
                "Coletar logos para interface",
                "Complementar dados da Live Results"
            ],
            "test_results": {
                "last_tested": "2025-07-25T00:00:00Z",
                "status": "FUNCIONANDO",
                "response_time_avg": "0.2s",
                "success_rate": "95%",
                "parsing_success_rate": "90%"
            }
        })
        
        # 3. API STANDINGS (5 MERCADOS)
        self.add_api_documentation("standings", {
            "name": "Standings API",
            "category": "Classificações e Estatísticas",
            "priority": "ALTA",
            "endpoint": "/football/{country}/{league}/standings/",
            "method": "GET",
            "description": "API versátil para classificações com múltiplos tipos de estatísticas",
            "url_pattern": "https://www.betexplorer.com/football/{country}/{league}/standings/",
            "url_example": "https://www.betexplorer.com/football/colombia/primera-a/standings/",
            "parameters": {
                "required": [
                    {
                        "name": "table",
                        "type": "string",
                        "description": "Tipo de tabela/mercado",
                        "options": ["table", "form", "over_under", "ht_ft", "top_scorers"],
                        "example": "over_under"
                    },
                    {
                        "name": "as-ajax",
                        "type": "integer",
                        "description": "Flag para requisição AJAX",
                        "value": 1
                    }
                ],
                "optional": [
                    {
                        "name": "table_sub",
                        "type": "string",
                        "description": "Subtipo da tabela",
                        "options": ["overall", "home", "away"],
                        "example": "overall"
                    },
                    {
                        "name": "ts",
                        "type": "string",
                        "description": "Token de sessão",
                        "example": "Oj4yELnn"
                    },
                    {
                        "name": "l",
                        "type": "string",
                        "description": "Idioma",
                        "example": "en"
                    }
                ]
            },
            "market_types": {
                "table": {
                    "name": "Classificação Geral",
                    "description": "Tabela tradicional com pontos, vitórias, empates, derrotas",
                    "size_typical": "37 KB",
                    "data_points": ["Posição", "Time", "Jogos", "Vitórias", "Empates", "Derrotas", "Gols", "Saldo", "Pontos"],
                    "sub_types": {
                        "overall": {
                            "name": "Classificação Geral",
                            "description": "Todos os jogos (casa + fora)",
                            "url_example": "?table=table&table_sub=overall"
                        },
                        "home": {
                            "name": "Classificação Mandante",
                            "description": "Apenas jogos em casa",
                            "url_example": "?table=table&table_sub=home",
                            "note": "Mostra performance dos times jogando em casa"
                        },
                        "away": {
                            "name": "Classificação Visitante",
                            "description": "Apenas jogos fora de casa",
                            "url_example": "?table=table&table_sub=away",
                            "note": "Mostra performance dos times jogando fora"
                        }
                    }
                },
                "form": {
                    "name": "Forma Recente",
                    "description": "Classificação baseada nos últimos jogos",
                    "size_typical": "37 KB",
                    "data_points": ["Posição", "Time", "Forma recente", "Pontos recentes"],
                    "sub_types": {
                        "overall": "Forma geral (casa + fora)",
                        "home": "Forma apenas em casa",
                        "away": "Forma apenas fora"
                    }
                },
                "over_under": {
                    "name": "Over/Under",
                    "description": "Estatísticas detalhadas de Over/Under por time",
                    "size_typical": "248 KB",
                    "data_points": ["Time", "Over 2.5", "Under 2.5", "Over 1.5", "Under 1.5", "Percentuais"],
                    "note": "MERCADO MAIS RICO EM DADOS - 6x maior que outros",
                    "sub_types": {
                        "overall": "Over/Under geral (casa + fora)",
                        "home": "Over/Under apenas em casa",
                        "away": "Over/Under apenas fora"
                    }
                },
                "ht_ft": {
                    "name": "Half Time / Full Time",
                    "description": "Estatísticas de meio-tempo vs tempo final",
                    "size_typical": "21 KB",
                    "data_points": ["Time", "HT Win", "HT Draw", "HT Loss", "FT Win", "FT Draw", "FT Loss"]
                },
                "top_scorers": {
                    "name": "Artilheiros",
                    "description": "Maiores goleadores da competição",
                    "size_typical": "57 KB",
                    "data_points": ["Posição", "Jogador", "Time", "Gols", "Jogos"]
                }
            },
            "response": {
                "format": "HTML",
                "parsing_required": "Sim - BeautifulSoup",
                "tables_count": "1-7 (Over/Under tem 7 tabelas)"
            },
            "use_cases": [
                "Análise de performance dos times",
                "Estatísticas para apostas Over/Under",
                "Identificação de artilheiros",
                "Análise de forma recente",
                "Comparação casa vs fora"
            ],
            "sub_types_explanation": {
                "overall": {
                    "description": "Dados gerais combinando jogos em casa e fora",
                    "use_case": "Visão completa da performance do time"
                },
                "home": {
                    "description": "Apenas jogos disputados em casa (mandante)",
                    "use_case": "Análise de vantagem do mando de campo",
                    "url_example": "?table=table&table_sub=home"
                },
                "away": {
                    "description": "Apenas jogos disputados fora de casa (visitante)",
                    "use_case": "Análise de performance como visitante",
                    "url_example": "?table=table&table_sub=away"
                }
            },
            "test_results": {
                "last_tested": "2025-07-25T00:14:00Z",
                "status": "FUNCIONANDO",
                "success_rate": "100%",
                "markets_tested": 5,
                "total_entries_collected": 100,
                "total_data_size": "401 KB",
                "sub_types_tested": ["overall", "home", "away"],
                "sub_types_working": "100%"
            }
        })
        
        # 4. API TEAM MATCHES (LAST RESULTS)
        self.add_api_documentation("team_matches", {
            "name": "Team Matches API (Last Results)",
            "category": "Histórico de Times",
            "priority": "ALTA",
            "endpoint": "/res/ajax/team-matches.php",
            "method": "GET",
            "description": "Retorna histórico de jogos de um time específico com controle de período",
            "url_example": "https://www.betexplorer.com/res/ajax/team-matches.php?par=MSw3OTgwNyxQcmltZXJhIEEgMjAyNSww&event=GWGODpB4&team=GWDKnMQE&type=2&count=9&lang=en",
            "parameters": {
                "required": [
                    {
                        "name": "par",
                        "type": "string",
                        "description": "Parâmetro codificado em base64 com info da competição",
                        "example": "MSw3OTgwNyxQcmltZXJhIEEgMjAyNSww",
                        "decoded_example": "1,79807,Primera A 2025,0",
                        "structure": "[flag, tournament_id, tournament_name, tipo]"
                    },
                    {
                        "name": "team",
                        "type": "string",
                        "description": "ID do time específico",
                        "example": "GWDKnMQE"
                    }
                ],
                "optional": [
                    {
                        "name": "event",
                        "type": "string",
                        "description": "Event ID de referência",
                        "example": "GWGODpB4"
                    },
                    {
                        "name": "type",
                        "type": "integer",
                        "description": "Tipo de histórico",
                        "options": {
                            "0": "Todos os jogos",
                            "1": "Apenas jogos em casa",
                            "2": "Apenas jogos fora de casa"
                        },
                        "example": 2
                    },
                    {
                        "name": "count",
                        "type": "integer",
                        "description": "Período em meses para buscar histórico",
                        "range": "3-9 meses",
                        "example": 9,
                        "note": "DESCOBERTA: Controla quantos meses de histórico retornar"
                    },
                    {
                        "name": "lang",
                        "type": "string",
                        "description": "Idioma",
                        "example": "en"
                    }
                ]
            },
            "response": {
                "format": "JSON/HTML",
                "size_typical": "5-15 KB",
                "content_type": "application/json"
            },
            "data_returned": [
                "Histórico de jogos do time específico",
                "Resultados por período (3-9 meses)",
                "Performance em casa vs fora",
                "Resultados recentes filtrados por tipo"
            ],
            "use_cases": [
                "Análise de performance de um time específico",
                "Histórico de jogos em casa vs fora",
                "Análise de forma recente (3-9 meses)",
                "Complementar dados para apostas"
            ],
            "parameter_details": {
                "count_values": {
                    "3": "Últimos 3 meses",
                    "6": "Últimos 6 meses",
                    "9": "Últimos 9 meses"
                },
                "type_values": {
                    "0": "Todos os jogos (casa + fora)",
                    "1": "Apenas mandante (jogos em casa)",
                    "2": "Apenas visitante (jogos fora)"
                }
            },
            "test_results": {
                "last_tested": "2025-07-25T03:20:00Z",
                "status": "FUNCIONANDO",
                "response_time_avg": "0.18s",
                "success_rate": "95%",
                "data_quality": "Alta - histórico detalhado"
            },
            "code_example": '''
# Exemplo: Últimos 6 meses de jogos fora do Dep. Cali
params = {
    'par': 'MSw3OTgwNyxQcmltZXJhIEEgMjAyNSww',  # Primera A 2025
    'team': 'GWDKnMQE',  # Dep. Cali
    'type': 2,  # Apenas jogos fora
    'count': 6,  # Últimos 6 meses
    'lang': 'en'
}
            '''
        })

        # 5. API MUTUAL MATCHES
        self.add_api_documentation("mutual_matches", {
            "name": "Mutual Matches API",
            "category": "Histórico H2H",
            "priority": "MÉDIA",
            "endpoint": "/gres/ajax/mutual-matches.php",
            "method": "GET",
            "description": "Retorna histórico de confrontos diretos entre dois times",
            "url_example": "https://www.betexplorer.com/gres/ajax/mutual-matches.php?par=MSwwLFVOczhXaXR0LEMyUWU3azM3&new=true&bt=1x2&lang=en",
            "parameters": {
                "required": [
                    {
                        "name": "par",
                        "type": "string",
                        "description": "Parâmetro codificado em base64 com info dos times",
                        "example": "MSwwLFVOczhXaXR0LEMyUWU3azM3",
                        "decoded_example": "1,0,UNs8Witt,C2Qe7k37",
                        "structure": "[flag, tipo, team_id1, team_id2]"
                    }
                ],
                "optional": [
                    {
                        "name": "new",
                        "type": "boolean",
                        "description": "Flag para nova versão",
                        "example": "true"
                    },
                    {
                        "name": "bt",
                        "type": "string", 
                        "description": "Betting type",
                        "example": "1x2"
                    }
                ]
            },
            "response": {
                "format": "HTML",
                "size_typical": "32 KB",
                "content_type": "text/html"
            },
            "data_returned": [
                "Histórico de confrontos diretos",
                "Resultados anteriores entre os times",
                "Estatísticas de vitórias/empates/derrotas",
                "Dados de H2H por competição"
            ],
            "limitations": [
                "Requer parâmetro codificado específico",
                "Não fornece estatísticas do jogo atual"
            ],
            "test_results": {
                "last_tested": "2025-07-25T00:00:00Z",
                "status": "FUNCIONANDO",
                "response_time_avg": "0.3s",
                "data_quality": "Boa - contém histórico real"
            }
        })
        
        # 6. API NEXT RESULTS
        self.add_api_documentation("next_results", {
            "name": "Next Results API", 
            "category": "Jogos Futuros",
            "priority": "BAIXA",
            "endpoint": "/gres/ajax/next-results.php",
            "method": "GET",
            "description": "Retorna próximos jogos/resultados futuros agendados",
            "url_example": "https://www.betexplorer.com/gres/ajax/next-results.php?isnext=3&msg=0&bt=ou&tab=upcoming",
            "parameters": {
                "optional": [
                    {
                        "name": "isnext",
                        "type": "integer",
                        "description": "Flag para próximos resultados",
                        "example": 3
                    },
                    {
                        "name": "tab",
                        "type": "string",
                        "description": "Aba de visualização",
                        "example": "upcoming"
                    }
                ]
            },
            "response": {
                "format": "JSON",
                "size_typical": "2 KB"
            },
            "data_returned": [
                "Próximos jogos agendados",
                "Eventos futuros",
                "Odds de jogos não iniciados"
            ],
            "test_results": {
                "last_tested": "2025-07-25T00:00:00Z", 
                "status": "FUNCIONANDO",
                "response_time_avg": "0.19s"
            }
        })
        
        return self.documentation
    
    def save_documentation(self, filename: str = "betexplorer_complete_api_docs.json") -> str:
        """
        Salva a documentação completa em arquivo JSON.
        
        Args:
            filename: Nome do arquivo
            
        Returns:
            Nome do arquivo salvo
        """
        # Gerar documentação completa
        complete_docs = self.generate_complete_documentation()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(complete_docs, f, indent=2, ensure_ascii=False)
            
            file_size = len(json.dumps(complete_docs, indent=2))
            
            print(f"📚 DOCUMENTAÇÃO COMPLETA GERADA!")
            print("="*50)
            print(f"📄 Arquivo: {filename}")
            print(f"📁 Tamanho: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            print(f"📊 Total de APIs: {complete_docs['project_info']['total_apis']}")
            print(f"🔄 Última atualização: {complete_docs['project_info']['last_updated']}")
            
            return filename
            
        except Exception as e:
            print(f"❌ Erro ao salvar documentação: {str(e)}")
            return ""
    
    def print_summary(self) -> None:
        """Imprime resumo da documentação."""
        docs = self.generate_complete_documentation()
        
        print(f"\n📚 RESUMO DA DOCUMENTAÇÃO")
        print("="*50)
        
        project_info = docs["project_info"]
        print(f"📊 Total de APIs documentadas: {project_info['total_apis']}")
        print(f"📅 Criado em: {project_info['created_at']}")
        print(f"🔄 Última atualização: {project_info['last_updated']}")
        
        print(f"\n🔗 APIs POR CATEGORIA:")
        
        categories = {}
        for api_id, api_data in docs["apis"].items():
            category = api_data.get("category", "Outros")
            if category not in categories:
                categories[category] = []
            categories[category].append({
                "name": api_data["name"],
                "priority": api_data.get("priority", "BAIXA"),
                "status": api_data.get("test_results", {}).get("status", "NÃO TESTADO")
            })
        
        for category, apis in categories.items():
            print(f"\n📂 {category}:")
            for api in apis:
                priority_icon = {"CRÍTICA": "🔴", "ALTA": "🟡", "MÉDIA": "🟢", "BAIXA": "⚪"}.get(api["priority"], "⚪")
                status_icon = {"FUNCIONANDO": "✅", "ERRO": "❌", "NÃO TESTADO": "⏸️"}.get(api["status"], "⏸️")
                print(f"   {priority_icon} {status_icon} {api['name']} ({api['priority']})")
        
        print(f"\n💡 FLUXO RECOMENDADO:")
        print("1️⃣ Live Results → Eventos básicos + Odds")
        print("2️⃣ Match Content → Nomes dos times + Competição")
        print("3️⃣ Standings → Classificações + Estatísticas")
        print("4️⃣ Team Matches → Histórico individual dos times (3-9 meses)")
        print("5️⃣ Mutual Matches → Histórico H2H (opcional)")
        print("6️⃣ Next Results → Jogos futuros (opcional)")


def main():
    """Função principal para gerar documentação."""
    print("📚 GERADOR DE DOCUMENTAÇÃO COMPLETA")
    print("="*60)
    
    # Criar documentação
    doc_generator = BetExplorerAPIDocumentation()
    
    # Salvar documentação
    filename = doc_generator.save_documentation()
    
    # Mostrar resumo
    doc_generator.print_summary()
    
    print(f"\n🎉 Documentação completa salva em: {filename}")
    
    return filename


if __name__ == '__main__':
    main()
