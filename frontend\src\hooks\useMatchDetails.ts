'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  MatchDetails, 
  BetExplorerResponse, 
  MatchDetailsHookResult 
} from '@/lib/betexplorer-types'

/**
 * Hook para buscar detalhes específicos de jogos do BetExplorer
 * Inclui cache inteligente e tratamento de erros
 */

interface UseMatchDetailsOptions {
  template?: string
  bettingType?: string
  token?: string
  lang?: string
  enabled?: boolean // Controla se deve fazer a requisição
}

export function useMatchDetails(
  eventId: string | null, 
  options: UseMatchDetailsOptions = {}
): MatchDetailsHookResult {
  const {
    template = 'new',
    bettingType = '1x2',
    token,
    lang = 'en',
    enabled = true
  } = options

  // Estados
  const [data, setData] = useState<MatchDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Refs para controle
  const abortControllerRef = useRef<AbortController | null>(null)
  const cacheRef = useRef<Map<string, {
    data: MatchDetails
    timestamp: number
  }>>(new Map())

  // Cache TTL: 5 minutos (detalhes de jogos mudam menos frequentemente)
  const CACHE_TTL = 5 * 60 * 1000

  // Função para buscar detalhes do jogo
  const fetchMatchDetails = useCallback(async (targetEventId: string) => {
    try {
      // Verificar cache primeiro
      const cached = cacheRef.current.get(targetEventId)
      const now = Date.now()
      
      if (cached && (now - cached.timestamp) < CACHE_TTL) {
        console.log(`📦 Usando cache para match details: ${targetEventId}`)
        setData(cached.data)
        setError(null)
        setLoading(false)
        return
      }

      setLoading(true)
      setError(null)

      // Cancelar requisição anterior se existir
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Criar novo AbortController
      abortControllerRef.current = new AbortController()

      // Construir URL da API
      const params = new URLSearchParams({
        eventId: targetEventId,
        template,
        bettingType,
        lang
      })

      if (token) {
        params.append('token', token)
      }

      const apiUrl = `/api/betexplorer/match-content?${params.toString()}`

      console.log(`🔄 Buscando detalhes do jogo: ${targetEventId}`)

      const response = await fetch(apiUrl, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`)
      }

      const result: BetExplorerResponse<MatchDetails> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Erro desconhecido na API')
      }

      if (!result.data) {
        throw new Error('Dados não encontrados na resposta')
      }

      // Salvar no cache
      cacheRef.current.set(targetEventId, {
        data: result.data,
        timestamp: now
      })

      // Atualizar estados
      setData(result.data)
      setError(null)

      console.log(`✅ Detalhes carregados: ${result.data.homeTeam} x ${result.data.awayTeam}`)

    } catch (err) {
      // Ignorar erros de abort (cancelamento)
      if (err instanceof Error && err.name === 'AbortError') {
        return
      }

      console.error('❌ Erro ao buscar detalhes do jogo:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }, [template, bettingType, token, lang])

  // Efeito para buscar dados quando eventId mudar
  useEffect(() => {
    if (!eventId || !enabled) {
      setData(null)
      setError(null)
      setLoading(false)
      return
    }

    fetchMatchDetails(eventId)
  }, [eventId, enabled, fetchMatchDetails])

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Função para refetch manual
  const refetch = useCallback(async () => {
    if (!eventId) return
    
    // Limpar cache para este evento
    cacheRef.current.delete(eventId)
    
    await fetchMatchDetails(eventId)
  }, [eventId, fetchMatchDetails])

  return {
    data,
    loading,
    error,
    refetch,
    eventId
  }
}

// Hook para buscar múltiplos detalhes de jogos
export function useMultipleMatchDetails(eventIds: string[]) {
  const [results, setResults] = useState<Record<string, MatchDetails>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchMultiple = useCallback(async () => {
    if (eventIds.length === 0) return

    setLoading(true)
    setError(null)

    try {
      const promises = eventIds.map(async (eventId) => {
        const response = await fetch(`/api/betexplorer/match-content?eventId=${eventId}`)
        const result: BetExplorerResponse<MatchDetails> = await response.json()
        
        if (result.success && result.data) {
          return { eventId, data: result.data }
        }
        return null
      })

      const responses = await Promise.allSettled(promises)
      const newResults: Record<string, MatchDetails> = {}

      responses.forEach((response) => {
        if (response.status === 'fulfilled' && response.value) {
          newResults[response.value.eventId] = response.value.data
        }
      })

      setResults(newResults)
      console.log(`✅ Detalhes carregados para ${Object.keys(newResults).length} jogos`)

    } catch (err) {
      console.error('❌ Erro ao buscar múltiplos detalhes:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }, [eventIds])

  useEffect(() => {
    fetchMultiple()
  }, [fetchMultiple])

  return {
    results,
    loading,
    error,
    refetch: fetchMultiple
  }
}

// Hook combinado para eventos ao vivo com detalhes
export function useLiveEventsWithDetails() {
  // Importar o hook de live results aqui para evitar dependência circular
  const { useLiveResults } = require('./useLiveResults')
  const { footballEvents, loading: eventsLoading, error: eventsError } = useLiveResults()

  // Pegar os primeiros 4 eventos para o dashboard
  const topEvents = footballEvents.slice(0, 4)
  const eventIds = topEvents.map(event => event.event_id)

  // Buscar detalhes para estes eventos
  const { results: matchDetails, loading: detailsLoading } = useMultipleMatchDetails(eventIds)

  // Combinar dados
  const eventsWithDetails = topEvents.map(event => ({
    ...event,
    homeTeam: matchDetails[event.event_id]?.homeTeam || event.homeTeam,
    awayTeam: matchDetails[event.event_id]?.awayTeam || event.awayTeam,
    homeTeamLogo: matchDetails[event.event_id]?.homeTeamLogo,
    awayTeamLogo: matchDetails[event.event_id]?.awayTeamLogo,
    competition: matchDetails[event.event_id]?.tournament || event.competition,
    country: matchDetails[event.event_id]?.country || event.country
  }))

  return {
    events: eventsWithDetails,
    loading: eventsLoading || detailsLoading,
    error: eventsError,
    eventsCount: eventsWithDetails.length
  }
}
